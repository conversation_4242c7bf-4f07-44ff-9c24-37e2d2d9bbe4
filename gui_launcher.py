#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI 啟動器 - 啟動 RBL 查詢工具的圖形化介面
"""

import sys
import os

# 確保可以導入 ui 模組
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from ui import RBLCheckerGUI
    
    def main():
        """啟動 GUI"""
        print("正在啟動 RBL 查詢工具 GUI...")
        app = RBLCheckerGUI()
        app.run()
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"導入錯誤: {e}")
    print("請確保所有必要的套件都已安裝:")
    print("pip install requests")
    sys.exit(1)
except Exception as e:
    print(f"啟動錯誤: {e}")
    sys.exit(1)
