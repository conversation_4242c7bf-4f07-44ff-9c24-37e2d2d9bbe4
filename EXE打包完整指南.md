# RBL 黑名單查詢工具 GUI 版本 - EXE 打包完整指南

## 🎯 目標

將 RBL 黑名單查詢工具的 GUI 版本（ui.py）打包成可在任何 Windows 系統上執行的 EXE 檔案，無需安裝 Python 環境。

## 📋 準備工作

### 1. 環境檢查
確保您的開發環境具備以下條件：
- ✅ Windows 10/11 系統
- ✅ Python 3.9+ 已安裝
- ✅ 專案檔案完整

### 2. 安裝 PyInstaller
```bash
pip install pyinstaller
```

### 3. 檢查專案檔案
確認以下檔案存在：
- `ui.py` - 主程式檔案
- `GUI_使用說明.md` - 使用說明
- `sample_domains.txt` - 範例檔案（可選）

## 🚀 快速打包（推薦）

### 方法一：使用批次檔
```batch
# 直接執行
build_gui.bat
```

### 方法二：使用 PowerShell
```powershell
# 以管理員身份執行 PowerShell
.\build_gui.ps1
```

### 方法三：使用 Python 腳本
```bash
# 快速打包
python quick_build.py

# 或完整打包（包含可攜式套件）
python build_exe.py
```

## 🔧 手動打包步驟

如果自動腳本無法使用，可以手動執行以下步驟：

### 1. 清理舊檔案
```bash
# 刪除舊的建置檔案
rmdir /s /q build
rmdir /s /q dist
del *.spec
```

### 2. 執行 PyInstaller
```bash
python -m PyInstaller ^
    --onefile ^
    --windowed ^
    --name=RBL_Checker_GUI ^
    --add-data="GUI_使用說明.md;." ^
    --add-data="sample_domains.txt;." ^
    --hidden-import=tkinter ^
    --hidden-import=tkinter.ttk ^
    --hidden-import=tkinter.messagebox ^
    --hidden-import=tkinter.filedialog ^
    --hidden-import=tkinter.scrolledtext ^
    --hidden-import=requests ^
    --hidden-import=concurrent.futures ^
    --hidden-import=threading ^
    --exclude-module=matplotlib ^
    --exclude-module=numpy ^
    --exclude-module=pandas ^
    --exclude-module=PIL ^
    ui.py
```

## 📁 輸出檔案

打包完成後，您將得到：

### 基本輸出
- `dist/RBL_Checker_GUI.exe` - 主要可執行檔案

### 完整套件（使用 build_exe.py）
```
RBL_Checker_GUI_Portable/
├── RBL_Checker_GUI.exe      # 主程式
├── GUI_使用說明.md          # 使用說明
├── sample_domains.txt       # 範例檔案
├── config.sample.json       # 範例設定檔
└── 使用說明.txt            # 快速指南
```

## ✅ 測試檢查清單

打包完成後，請進行以下測試：

### 基本功能測試
- [ ] 程式能正常啟動
- [ ] GUI 介面顯示正常
- [ ] 所有按鈕和選單可正常點擊
- [ ] API 設定視窗能正常開啟
- [ ] 檔案匯入功能正常
- [ ] 程式能正常關閉

### 進階功能測試（需要 API 認證）
- [ ] API 連線測試功能
- [ ] RBL 查詢功能
- [ ] 結果顯示正常
- [ ] CSV/JSON 匯出功能

## 🐛 常見問題解決

### 問題 1: PyInstaller 未安裝
```bash
# 解決方案
pip install pyinstaller
```

### 問題 2: 打包失敗 - 模組錯誤
```bash
# 檢查並安裝缺少的依賴
pip install requests
pip install -r requirements.txt
```

### 問題 3: EXE 檔案無法執行
- 檢查是否使用了 `--windowed` 參數
- 確認目標系統是 64 位元 Windows
- 檢查防毒軟體是否阻擋

### 問題 4: tkinter 相關錯誤
```bash
# 確保包含所有 tkinter 模組
--hidden-import=tkinter
--hidden-import=tkinter.ttk
--hidden-import=tkinter.messagebox
--hidden-import=tkinter.filedialog
--hidden-import=tkinter.scrolledtext
```

### 問題 5: 檔案過大
- 使用更多 `--exclude-module` 參數
- 考慮使用 `--onedir` 而非 `--onefile`

## 📊 檔案大小參考

預期的 EXE 檔案大小：
- **基本版本**: 約 15-25 MB
- **包含所有依賴**: 約 20-35 MB

## 🔒 安全性注意事項

### 防毒軟體誤報
- PyInstaller 打包的程式可能被防毒軟體誤報
- 建議將程式加入白名單
- 或考慮程式碼簽名（需要憑證）

### 程式碼保護
- PyInstaller 不提供程式碼加密
- 如需保護原始碼，考慮其他打包工具

## 🚀 部署建議

### 1. 檔案分發
- 建議壓縮 EXE 檔案後分發
- 提供 MD5 或 SHA256 校驗碼

### 2. 使用說明
- 隨 EXE 檔案提供使用說明
- 說明 API 設定步驟

### 3. 版本管理
- 在檔案名稱中包含版本號
- 例如：`RBL_Checker_GUI_v1.0.exe`

## 📞 技術支援

如果遇到打包問題：

1. 檢查 Python 和 PyInstaller 版本
2. 確認所有依賴已正確安裝
3. 查看 PyInstaller 的詳細錯誤訊息
4. 參考 `打包說明.md` 中的詳細說明

## 🎉 完成！

成功打包後，您將擁有一個可在任何 Windows 系統上執行的 RBL 黑名單查詢工具！

**使用方式：**
1. 將 `RBL_Checker_GUI.exe` 複製到目標電腦
2. 雙擊執行
3. 點擊「API 設定」配置認證資訊
4. 開始使用 RBL 查詢功能

**檔案位置：** `dist/RBL_Checker_GUI.exe`
