# RBL 黑名單查詢工具 GUI 版本 - EXE 打包說明

## 概述

本文件說明如何將 RBL 黑名單查詢工具的 GUI 版本打包成可執行的 EXE 檔案，讓使用者可以在沒有安裝 Python 的 Windows 系統上直接執行。

## 系統需求

### 開發環境需求
- Windows 10/11 (推薦)
- Python 3.9 或更高版本
- PyInstaller 套件
- 所有專案依賴套件 (requests 等)

### 目標系統需求
- Windows 7/8/10/11 (64位元)
- 無需安裝 Python 或其他依賴

## 打包工具選擇

我們使用 **PyInstaller** 作為主要打包工具，原因如下：
- ✅ 成熟穩定，廣泛使用
- ✅ 支援 tkinter GUI 應用程式
- ✅ 可打包成單一 EXE 檔案
- ✅ 自動處理依賴項目
- ✅ 支援無控制台視窗模式

## 打包方式

### 方式一：使用批次檔（推薦）

**Windows 批次檔：**
```batch
build_gui.bat
```

**PowerShell 腳本：**
```powershell
.\build_gui.ps1
```

### 方式二：使用 Python 腳本

**完整打包腳本：**
```bash
python build_exe.py
```

**快速打包腳本：**
```bash
python quick_build.py
```

### 方式三：手動命令列

```bash
# 安裝 PyInstaller
pip install pyinstaller

# 執行打包
python -m PyInstaller --onefile --windowed --name=RBL_Checker_GUI ui.py
```

## 打包參數說明

### 主要參數
- `--onefile`: 打包成單一 EXE 檔案
- `--windowed`: 無控制台視窗（GUI 模式）
- `--name=RBL_Checker_GUI`: 指定輸出檔案名稱

### 資料檔案
- `--add-data="GUI_使用說明.md;."`: 包含使用說明
- `--add-data="sample_domains.txt;."`: 包含範例檔案

### 隱藏匯入
```
--hidden-import=tkinter
--hidden-import=tkinter.ttk
--hidden-import=tkinter.messagebox
--hidden-import=tkinter.filedialog
--hidden-import=tkinter.scrolledtext
--hidden-import=requests
--hidden-import=concurrent.futures
--hidden-import=threading
```

### 排除模組
```
--exclude-module=matplotlib
--exclude-module=numpy
--exclude-module=pandas
--exclude-module=PIL
```

## 打包流程

### 1. 環境準備
```bash
# 確認 Python 版本
python --version

# 安裝 PyInstaller
pip install pyinstaller

# 安裝專案依賴
pip install -r requirements.txt
```

### 2. 執行打包
選擇以下任一方式：

**方式 A - 批次檔（最簡單）：**
```batch
build_gui.bat
```

**方式 B - Python 腳本：**
```bash
python build_exe.py
```

**方式 C - 手動命令：**
```bash
python -m PyInstaller --onefile --windowed --name=RBL_Checker_GUI ui.py
```

### 3. 檢查結果
打包完成後，檢查以下檔案：
- `dist/RBL_Checker_GUI.exe` - 主要可執行檔案
- `RBL_Checker_GUI_Portable/` - 可攜式套件目錄（如使用完整腳本）

## 檔案結構

### 打包前
```
專案目錄/
├── ui.py                    # 主程式
├── GUI_使用說明.md          # 使用說明
├── sample_domains.txt       # 範例檔案
├── requirements.txt         # 依賴清單
├── build_exe.py            # 完整打包腳本
├── quick_build.py          # 快速打包腳本
├── build_gui.bat           # Windows 批次檔
└── build_gui.ps1           # PowerShell 腳本
```

### 打包後
```
專案目錄/
├── dist/
│   └── RBL_Checker_GUI.exe # 可執行檔案
├── build/                  # 建置暫存檔案
├── RBL_Checker_GUI.spec    # PyInstaller 規格檔
└── RBL_Checker_GUI_Portable/ # 可攜式套件
    ├── RBL_Checker_GUI.exe
    ├── GUI_使用說明.md
    ├── sample_domains.txt
    ├── config.sample.json
    └── 使用說明.txt
```

## 測試與驗證

### 基本功能測試
1. ✅ 程式能正常啟動
2. ✅ GUI 介面顯示正常
3. ✅ API 設定功能正常
4. ✅ 檔案匯入功能正常
5. ✅ 查詢功能正常（需 API 認證）
6. ✅ 結果匯出功能正常
7. ✅ 程式能正常關閉

### 相容性測試
- ✅ Windows 10 (64位元)
- ✅ Windows 11 (64位元)
- ⚠️ Windows 7/8 (可能需要額外測試)

## 常見問題與解決方案

### 問題 1: PyInstaller 安裝失敗
**解決方案：**
```bash
# 升級 pip
python -m pip install --upgrade pip

# 重新安裝 PyInstaller
pip install --upgrade pyinstaller
```

### 問題 2: 打包過程中出現模組錯誤
**解決方案：**
- 檢查 `requirements.txt` 是否包含所有依賴
- 使用 `--hidden-import` 參數明確指定模組

### 問題 3: EXE 檔案過大
**解決方案：**
- 使用 `--exclude-module` 排除不必要的模組
- 考慮使用 `--onedir` 模式而非 `--onefile`

### 問題 4: 執行時出現 tkinter 錯誤
**解決方案：**
- 確保使用 `--windowed` 參數
- 添加 `--hidden-import=tkinter` 等相關匯入

### 問題 5: 防毒軟體誤報
**解決方案：**
- 這是 PyInstaller 打包程式的常見問題
- 可以將 EXE 檔案加入防毒軟體白名單
- 或考慮程式碼簽名（需要憑證）

## 最佳實踐

### 1. 版本管理
- 在檔案名稱中包含版本號
- 保留不同版本的 EXE 檔案

### 2. 檔案大小優化
- 排除不必要的模組
- 使用 UPX 壓縮（可選）

### 3. 使用者體驗
- 提供詳細的使用說明
- 包含範例檔案和設定檔
- 考慮製作安裝程式

### 4. 測試流程
- 在乾淨的系統上測試
- 測試所有主要功能
- 檢查檔案路徑和權限

## 進階選項

### 自訂圖示
```bash
--icon=icon.ico
```

### 版本資訊
```bash
--version-file=version.txt
```

### 程式碼簽名
```bash
--codesign-identity="Developer ID"
```

## 總結

透過以上步驟，您可以成功將 RBL 黑名單查詢工具的 GUI 版本打包成獨立的 EXE 檔案。建議使用提供的批次檔或 Python 腳本來簡化打包流程，確保包含所有必要的檔案和設定。
