#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量檢查視窗模組
提供批量域名檢查的使用者介面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import logging
from typing import Callable, List

from models.domain_validator import DomainValidator

class BatchWindow:
    """批量檢查視窗類別"""
    
    def __init__(self, parent: tk.Tk, rbl_checker, result_callback: Callable):
        self.parent = parent
        self.rbl_checker = rbl_checker
        self.result_callback = result_callback
        self.logger = logging.getLogger(__name__)
        
        # 初始化元件
        self.domain_validator = DomainValidator()
        
        # 視窗狀態
        self.is_checking = False
        self.check_thread = None
        
        # 建立視窗
        self.create_window()
        self.create_widgets()
        self.setup_layout()
        
        self.logger.info("批量檢查視窗初始化完成")
    
    def create_window(self):
        """建立批量檢查視窗"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("批量域名檢查")
        self.window.geometry("600x500")
        self.window.minsize(500, 400)
        
        # 設定為模態視窗
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # 置中顯示
        self.center_window()
        
        # 設定關閉事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def center_window(self):
        """將視窗置中顯示"""
        self.window.update_idletasks()
        
        # 取得視窗大小
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        
        # 取得螢幕大小
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        
        # 計算置中位置
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """建立所有 UI 元件"""
        # 主框架
        self.main_frame = ttk.Frame(self.window, padding="10")
        
        # 標題
        self.title_label = ttk.Label(
            self.main_frame,
            text="批量域名檢查",
            font=("Arial", 14, "bold")
        )
        
        # 輸入區域
        self.input_frame = ttk.LabelFrame(self.main_frame, text="域名輸入", padding="10")
        
        self.input_label = ttk.Label(
            self.input_frame,
            text="請輸入要檢查的域名（每行一個）："
        )
        
        # 文字輸入區域
        self.text_frame = ttk.Frame(self.input_frame)
        
        self.domain_text = tk.Text(
            self.text_frame,
            height=10,
            width=50,
            font=("Consolas", 10),
            wrap=tk.WORD
        )
        
        # 滾動條
        self.text_scrollbar = ttk.Scrollbar(
            self.text_frame,
            orient="vertical",
            command=self.domain_text.yview
        )
        self.domain_text.configure(yscrollcommand=self.text_scrollbar.set)
        
        # 檔案操作按鈕
        self.file_button_frame = ttk.Frame(self.input_frame)
        
        self.load_file_button = ttk.Button(
            self.file_button_frame,
            text="從檔案載入",
            command=self.load_from_file
        )
        
        self.clear_text_button = ttk.Button(
            self.file_button_frame,
            text="清除",
            command=self.clear_text
        )
        
        # 設定區域
        self.settings_frame = ttk.LabelFrame(self.main_frame, text="檢查設定", padding="10")
        
        # 並發數設定
        self.concurrent_label = ttk.Label(self.settings_frame, text="並發檢查數量:")
        self.concurrent_var = tk.IntVar(value=5)
        self.concurrent_spinbox = ttk.Spinbox(
            self.settings_frame,
            from_=1,
            to=10,
            textvariable=self.concurrent_var,
            width=10
        )
        
        # 進度區域
        self.progress_frame = ttk.LabelFrame(self.main_frame, text="檢查進度", padding="10")
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.progress_frame,
            variable=self.progress_var,
            maximum=100,
            mode='determinate'
        )
        
        self.progress_label_var = tk.StringVar(value="就緒")
        self.progress_label = ttk.Label(
            self.progress_frame,
            textvariable=self.progress_label_var
        )
        
        # 按鈕區域
        self.button_frame = ttk.Frame(self.main_frame)
        
        self.start_button = ttk.Button(
            self.button_frame,
            text="開始批量檢查",
            command=self.start_batch_check,
            style="Accent.TButton"
        )
        
        self.stop_button = ttk.Button(
            self.button_frame,
            text="停止檢查",
            command=self.stop_batch_check,
            state="disabled"
        )
        
        self.close_button = ttk.Button(
            self.button_frame,
            text="關閉",
            command=self.on_closing
        )
    
    def setup_layout(self):
        """設定 UI 佈局"""
        # 主框架
        self.main_frame.grid(row=0, column=0, sticky="nsew")
        
        # 設定視窗的網格權重
        self.window.grid_rowconfigure(0, weight=1)
        self.window.grid_columnconfigure(0, weight=1)
        
        # 設定主框架的網格權重
        self.main_frame.grid_rowconfigure(1, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)
        
        # 標題
        self.title_label.grid(row=0, column=0, pady=(0, 10))
        
        # 輸入區域
        self.input_frame.grid(row=1, column=0, sticky="nsew", pady=(0, 10))
        self.input_frame.grid_rowconfigure(1, weight=1)
        self.input_frame.grid_columnconfigure(0, weight=1)
        
        self.input_label.grid(row=0, column=0, sticky="w", pady=(0, 5))
        
        # 文字輸入區域
        self.text_frame.grid(row=1, column=0, sticky="nsew", pady=(0, 10))
        self.text_frame.grid_rowconfigure(0, weight=1)
        self.text_frame.grid_columnconfigure(0, weight=1)
        
        self.domain_text.grid(row=0, column=0, sticky="nsew")
        self.text_scrollbar.grid(row=0, column=1, sticky="ns")
        
        # 檔案操作按鈕
        self.file_button_frame.grid(row=2, column=0, sticky="ew")
        self.load_file_button.grid(row=0, column=0, padx=(0, 5))
        self.clear_text_button.grid(row=0, column=1)
        
        # 設定區域
        self.settings_frame.grid(row=2, column=0, sticky="ew", pady=(0, 10))
        self.concurrent_label.grid(row=0, column=0, sticky="w")
        self.concurrent_spinbox.grid(row=0, column=1, padx=(10, 0))
        
        # 進度區域
        self.progress_frame.grid(row=3, column=0, sticky="ew", pady=(0, 10))
        self.progress_frame.grid_columnconfigure(0, weight=1)
        
        self.progress_bar.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        self.progress_label.grid(row=1, column=0, sticky="w")
        
        # 按鈕區域
        self.button_frame.grid(row=4, column=0, sticky="ew")
        self.start_button.grid(row=0, column=0, padx=(0, 5))
        self.stop_button.grid(row=0, column=1, padx=5)
        self.close_button.grid(row=0, column=2, padx=(5, 0))
    
    def load_from_file(self):
        """從檔案載入域名列表"""
        filename = filedialog.askopenfilename(
            title="選擇域名檔案",
            filetypes=[
                ("文字檔案", "*.txt"),
                ("CSV 檔案", "*.csv"),
                ("所有檔案", "*.*")
            ]
        )
        
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 清除現有內容並插入新內容
                self.domain_text.delete(1.0, tk.END)
                self.domain_text.insert(1.0, content)
                
                messagebox.showinfo("成功", f"已從 {filename} 載入域名列表")
                
            except Exception as e:
                messagebox.showerror("錯誤", f"載入檔案失敗: {str(e)}")
    
    def clear_text(self):
        """清除文字輸入區域"""
        if messagebox.askyesno("確認", "確定要清除所有輸入的域名嗎？"):
            self.domain_text.delete(1.0, tk.END)
    
    def start_batch_check(self):
        """開始批量檢查"""
        # 取得輸入的域名
        content = self.domain_text.get(1.0, tk.END).strip()
        if not content:
            messagebox.showwarning("警告", "請輸入要檢查的域名")
            return
        
        # 解析域名列表
        domains = [line.strip() for line in content.split('\n') if line.strip()]
        
        if not domains:
            messagebox.showwarning("警告", "沒有找到有效的域名")
            return
        
        # 驗證域名
        valid_domains, invalid_domains = self.domain_validator.validate_domain_list(domains)
        
        if invalid_domains:
            invalid_list = '\n'.join(invalid_domains[:10])  # 最多顯示 10 個
            if len(invalid_domains) > 10:
                invalid_list += f'\n... 還有 {len(invalid_domains) - 10} 個無效域名'
            
            message = f"發現 {len(invalid_domains)} 個無效域名:\n{invalid_list}\n\n是否繼續檢查有效的 {len(valid_domains)} 個域名？"
            
            if not messagebox.askyesno("確認", message):
                return
        
        if not valid_domains:
            messagebox.showerror("錯誤", "沒有有效的域名可以檢查")
            return
        
        # 開始檢查
        self.set_checking_state(True)
        self.progress_var.set(0)
        self.progress_label_var.set(f"準備檢查 {len(valid_domains)} 個域名...")
        
        # 在新執行緒中執行批量檢查
        max_workers = self.concurrent_var.get()
        self.check_thread = threading.Thread(
            target=self._batch_check_thread,
            args=(valid_domains, max_workers),
            daemon=True
        )
        self.check_thread.start()
    
    def _batch_check_thread(self, domains: List[str], max_workers: int):
        """在背景執行緒中執行批量檢查"""
        try:
            def progress_callback(completed: int, total: int):
                progress = (completed / total) * 100
                self.window.after(0, self._update_progress, progress, completed, total)
            
            results = self.rbl_checker.check_multiple_domains(
                domains, max_workers, progress_callback
            )
            
            # 在主執行緒中處理結果
            self.window.after(0, self._handle_batch_results, results)
            
        except Exception as e:
            self.logger.error(f"批量檢查時發生錯誤: {str(e)}")
            self.window.after(0, self._show_batch_error, str(e))
        finally:
            self.window.after(0, lambda: self.set_checking_state(False))
    
    def _update_progress(self, progress: float, completed: int, total: int):
        """更新進度"""
        self.progress_var.set(progress)
        self.progress_label_var.set(f"已完成 {completed}/{total} 個域名檢查")
    
    def _handle_batch_results(self, results: List[dict]):
        """處理批量檢查結果"""
        self.progress_var.set(100)
        self.progress_label_var.set(f"批量檢查完成，共檢查 {len(results)} 個域名")
        
        # 將結果傳遞給主視窗
        self.result_callback(results)
        
        # 顯示完成訊息
        stats = self.rbl_checker.get_summary_stats(results)
        message = (
            f"批量檢查完成！\n\n"
            f"總域名數: {stats['total_domains']}\n"
            f"成功檢查: {stats['successful_checks']}\n"
            f"檢查失敗: {stats['failed_checks']}\n"
            f"發現問題: {stats['listed_domains']}\n"
            f"狀態正常: {stats['clean_domains']}"
        )
        
        messagebox.showinfo("完成", message)
    
    def _show_batch_error(self, error_message: str):
        """顯示批量檢查錯誤"""
        messagebox.showerror("錯誤", f"批量檢查失敗: {error_message}")
        self.progress_label_var.set("檢查失敗")
    
    def stop_batch_check(self):
        """停止批量檢查"""
        if messagebox.askyesno("確認", "確定要停止批量檢查嗎？"):
            # 注意：這裡只是設定狀態，實際的執行緒停止需要更複雜的實作
            self.set_checking_state(False)
            self.progress_label_var.set("檢查已停止")
    
    def set_checking_state(self, is_checking: bool):
        """設定檢查狀態"""
        self.is_checking = is_checking
        
        # 啟用/停用按鈕
        self.start_button.config(state="disabled" if is_checking else "normal")
        self.stop_button.config(state="normal" if is_checking else "disabled")
        self.load_file_button.config(state="disabled" if is_checking else "normal")
        self.clear_text_button.config(state="disabled" if is_checking else "normal")
        self.domain_text.config(state="disabled" if is_checking else "normal")
        self.concurrent_spinbox.config(state="disabled" if is_checking else "normal")
    
    def on_closing(self):
        """視窗關閉事件"""
        if self.is_checking:
            if not messagebox.askyesno("確認", "檢查正在進行中，確定要關閉視窗嗎？"):
                return
        
        self.window.destroy()
