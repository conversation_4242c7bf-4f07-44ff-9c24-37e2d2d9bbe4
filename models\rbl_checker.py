#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RBL 檢查器核心模組
負責執行實際的 RBL 查詢和結果處理
"""

import requests
import time
import logging
import re
from typing import Dict, List, Tuple, Optional
from urllib.parse import urljoin
from bs4 import BeautifulSoup
import concurrent.futures
from threading import Lock

class RBLChecker:
    """RBL 檢查器主類別"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.results_lock = Lock()
        
        # MXToolBox 相關設定
        self.base_url = "https://mxtoolbox.com"
        self.blacklist_url = "https://mxtoolbox.com/blacklists.aspx"
        
        # RBL 服務列表
        self.rbl_services = [
            "Spamhaus SBL",
            "Spamhaus CSS",
            "Spamhaus XBL", 
            "Spamhaus PBL",
            "Barracuda",
            "SURBL",
            "URIBL",
            "SpamCop",
            "Invaluement",
            "PSBL"
        ]
    
    def validate_domain(self, domain: str) -> bool:
        """驗證域名格式"""
        domain_pattern = re.compile(
            r'^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$'
        )
        return bool(domain_pattern.match(domain.strip()))
    
    def check_single_domain(self, domain: str) -> Dict:
        """檢查單一域名的 RBL 狀態"""
        self.logger.info(f"開始檢查域名: {domain}")
        
        if not self.validate_domain(domain):
            return {
                'domain': domain,
                'status': 'error',
                'message': '無效的域名格式',
                'results': {}
            }
        
        try:
            # 構建查詢 URL
            query_url = f"{self.blacklist_url}?domain={domain}"
            
            # 發送請求
            response = self.session.get(query_url, timeout=30)
            response.raise_for_status()
            
            # 解析結果
            results = self._parse_mxtoolbox_response(response.text, domain)
            
            return {
                'domain': domain,
                'status': 'success',
                'message': '檢查完成',
                'results': results,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except requests.RequestException as e:
            self.logger.error(f"網路請求錯誤 - {domain}: {str(e)}")
            return {
                'domain': domain,
                'status': 'error',
                'message': f'網路連線錯誤: {str(e)}',
                'results': {}
            }
        except Exception as e:
            self.logger.error(f"檢查域名時發生錯誤 - {domain}: {str(e)}")
            return {
                'domain': domain,
                'status': 'error',
                'message': f'檢查失敗: {str(e)}',
                'results': {}
            }
    
    def _parse_mxtoolbox_response(self, html_content: str, domain: str) -> Dict:
        """解析 MXToolBox 回應內容"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            results = {}
            
            # 查找黑名單檢查結果表格
            tables = soup.find_all('table', class_='tool-result-table')
            
            for table in tables:
                rows = table.find_all('tr')
                for row in rows[1:]:  # 跳過標題行
                    cells = row.find_all('td')
                    if len(cells) >= 3:
                        rbl_name = cells[0].get_text(strip=True)
                        status = cells[1].get_text(strip=True)
                        details = cells[2].get_text(strip=True) if len(cells) > 2 else ""
                        
                        # 判斷狀態
                        is_listed = 'listed' in status.lower() or 'blocked' in status.lower()
                        
                        results[rbl_name] = {
                            'status': 'LISTED' if is_listed else 'CLEAN',
                            'details': details,
                            'raw_status': status
                        }
            
            # 如果沒有找到結果，嘗試其他解析方法
            if not results:
                results = self._fallback_parse(soup, domain)
            
            return results
            
        except Exception as e:
            self.logger.error(f"解析 HTML 內容時發生錯誤: {str(e)}")
            return {}
    
    def _fallback_parse(self, soup: BeautifulSoup, domain: str) -> Dict:
        """備用解析方法"""
        results = {}
        
        # 模擬基本的 RBL 檢查結果
        for service in self.rbl_services:
            results[service] = {
                'status': 'UNKNOWN',
                'details': '無法取得檢查結果',
                'raw_status': 'No data available'
            }
        
        return results
    
    def check_multiple_domains(self, domains: List[str], max_workers: int = 5, 
                             progress_callback=None) -> List[Dict]:
        """批量檢查多個域名"""
        self.logger.info(f"開始批量檢查 {len(domains)} 個域名")
        
        results = []
        completed = 0
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任務
            future_to_domain = {
                executor.submit(self.check_single_domain, domain.strip()): domain.strip() 
                for domain in domains if domain.strip()
            }
            
            # 收集結果
            for future in concurrent.futures.as_completed(future_to_domain):
                domain = future_to_domain[future]
                try:
                    result = future.result()
                    with self.results_lock:
                        results.append(result)
                        completed += 1
                    
                    # 呼叫進度回調
                    if progress_callback:
                        progress_callback(completed, len(future_to_domain))
                        
                except Exception as e:
                    self.logger.error(f"處理域名 {domain} 時發生錯誤: {str(e)}")
                    with self.results_lock:
                        results.append({
                            'domain': domain,
                            'status': 'error',
                            'message': f'處理錯誤: {str(e)}',
                            'results': {}
                        })
                        completed += 1
                    
                    if progress_callback:
                        progress_callback(completed, len(future_to_domain))
        
        self.logger.info(f"批量檢查完成，共處理 {len(results)} 個域名")
        return results
    
    def get_summary_stats(self, results: List[Dict]) -> Dict:
        """取得檢查結果統計"""
        stats = {
            'total_domains': len(results),
            'successful_checks': 0,
            'failed_checks': 0,
            'listed_domains': 0,
            'clean_domains': 0,
            'unknown_domains': 0
        }
        
        for result in results:
            if result['status'] == 'success':
                stats['successful_checks'] += 1
                
                # 分析 RBL 結果
                rbl_results = result.get('results', {})
                has_listing = any(
                    rbl_data.get('status') == 'LISTED' 
                    for rbl_data in rbl_results.values()
                )
                
                if has_listing:
                    stats['listed_domains'] += 1
                elif rbl_results:
                    stats['clean_domains'] += 1
                else:
                    stats['unknown_domains'] += 1
            else:
                stats['failed_checks'] += 1
        
        return stats
