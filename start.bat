@echo off
chcp 65001 >nul
title RBL Email Domains Status Checker

echo ========================================
echo RBL Email Domains Status Checker
echo ========================================
echo.

REM 檢查 Python 是否已安裝
py --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 錯誤: 未找到 Python
    echo 請先安裝 Python 3.7 或更新版本
    echo 下載地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 檢查 Python 版本...
py --version

REM 嘗試安裝相依套件（如果需要）
echo.
echo 檢查相依套件...
py -c "import tkinter; print('tkinter: OK')" 2>nul
if %errorlevel% neq 0 (
    echo 警告: tkinter 不可用，請確認 Python 安裝包含 tkinter
)

REM 啟動應用程式
echo.
echo 啟動 RBL Email Domains Status Checker...
echo.
py main.py

REM 如果程式異常結束，暫停以查看錯誤訊息
if %errorlevel% neq 0 (
    echo.
    echo 程式異常結束，錯誤代碼: %errorlevel%
    echo.
    echo 如果遇到問題，請嘗試：
    echo 1. 確認 Python 版本為 3.7 或更新
    echo 2. 執行: pip install -r requirements.txt
    echo 3. 或直接執行: py demo_main.py
    pause
)

echo.
echo 程式已結束
pause
