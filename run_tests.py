#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試執行腳本
執行所有測試並產生報告
"""

import unittest
import sys
import os
from io import StringIO

def discover_and_run_tests():
    """發現並執行所有測試"""
    # 添加專案根目錄到路徑
    project_root = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, project_root)
    
    # 發現測試
    loader = unittest.TestLoader()
    start_dir = os.path.join(project_root, 'tests')
    
    if not os.path.exists(start_dir):
        print(f"錯誤: 測試目錄不存在 - {start_dir}")
        return False
    
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    # 執行測試
    stream = StringIO()
    runner = unittest.TextTestRunner(
        stream=stream,
        verbosity=2,
        buffer=True
    )
    
    print("執行測試...")
    print("=" * 50)
    
    result = runner.run(suite)
    
    # 輸出結果
    output = stream.getvalue()
    print(output)
    
    # 總結
    print("=" * 50)
    print("測試總結:")
    print(f"執行測試數: {result.testsRun}")
    print(f"失敗數: {len(result.failures)}")
    print(f"錯誤數: {len(result.errors)}")
    print(f"跳過數: {len(result.skipped)}")
    
    if result.failures:
        print("\n失敗的測試:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\n錯誤的測試:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback.split('Exception:')[-1].strip()}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("\n✓ 所有測試通過！")
    else:
        print("\n❌ 有測試失敗或錯誤")
    
    return success

def run_specific_test(test_module):
    """執行特定測試模組"""
    try:
        # 動態匯入測試模組
        module = __import__(f'tests.{test_module}', fromlist=[test_module])
        
        # 建立測試套件
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromModule(module)
        
        # 執行測試
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return len(result.failures) == 0 and len(result.errors) == 0
        
    except ImportError as e:
        print(f"錯誤: 無法匯入測試模組 {test_module} - {e}")
        return False

def main():
    """主函數"""
    if len(sys.argv) > 1:
        # 執行特定測試
        test_module = sys.argv[1]
        print(f"執行特定測試: {test_module}")
        success = run_specific_test(test_module)
    else:
        # 執行所有測試
        print("RBL Email Domains Status Checker 測試執行器")
        success = discover_and_run_tests()
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
