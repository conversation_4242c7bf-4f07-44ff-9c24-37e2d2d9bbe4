<# 
    run_example.ps1 - RBL Blacklist 批次查詢 PowerShell 版
    用法：
      互動模式：  .\run_example.ps1
      參數模式：  .\run_example.ps1 -Provider hetrixtools -TargetsPath .\targets.txt -PythonExe py -TimeoutSec 300
#>

[CmdletBinding()]
param(
    [ValidateSet('hetrixtools','rbltracker')]
    [string]$Provider,

    # 目標清單（.txt/.csv/.tsv；第一欄會被讀取）
    [string]$TargetsPath = "targets.txt",

    # Python 可執行檔（預設 Windows 上的 py 啟動器）
    [string]$PythonExe = "py",

    # main.py 的 --timeout（輪詢最大等待秒數）
    [int]$TimeoutSec = 300
)

function Fail($msg) {
    Write-Host "[錯誤] $msg" -ForegroundColor Red
    exit 1
}

Write-Host "==============================================="
Write-Host "  RBL Blacklist 批次查詢工具 - PowerShell"
Write-Host "==============================================="

# 若未以參數指定 Provider，提供互動式選單
if (-not $Provider) {
    Write-Host ""
    Write-Host "請選擇要使用的 Provider：" -ForegroundColor Cyan
    Write-Host "  1) HetrixTools"
    Write-Host "  2) RBLTracker"
    $choice = Read-Host "輸入數字 (1 或 2)"
    switch ($choice) {
        '1' { $Provider = 'hetrixtools' }
        '2' { $Provider = 'rbltracker' }
        default { Fail "無效的選項，請輸入 1 或 2。" }
    }
}

# 檢查 targets 檔案
if (-not (Test-Path -LiteralPath $TargetsPath)) {
    Fail "找不到目標清單檔：$TargetsPath"
}

# 檢查 main.py 是否存在
if (-not (Test-Path -LiteralPath ".\main.py")) {
    Fail "找不到 main.py，請確認你在專案根目錄執行此指令。"
}

# 檢查必要環境變數
switch ($Provider) {
    'hetrixtools' {
        if (-not $env:HETRIX_API_TOKEN) {
            Fail "未設定環境變數 HETRIX_API_TOKEN。請先執行：`setx HETRIX_API_TOKEN ""你的_TOKEN""`，並重新開啟視窗。"
        }
    }
    'rbltracker' {
        if (-not $env:RBLTRACKER_SID -or -not $env:RBLTRACKER_TOKEN) {
            Fail "未設定 RBLTRACKER_SID / RBLTRACKER_TOKEN。請先執行：`setx RBLTRACKER_SID ""你的SID""` 與 `setx RBLTRACKER_TOKEN ""你的TOKEN""`，並重新開啟視窗。"
        }
    }
}

# 準備輸出檔名
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
switch ($Provider) {
    'hetrixtools' { $OutFile = "results_hetrix_$timestamp.csv" }
    'rbltracker'  { $OutFile = "results_rbl_$timestamp.json" }
}

Write-Host ""
Write-Host "Provider   : $Provider"
Write-Host "Targets    : $TargetsPath"
Write-Host "Python     : $PythonExe"
Write-Host "Timeout(s) : $TimeoutSec"
Write-Host "Output     : $OutFile"
Write-Host "-----------------------------------------------"

# 組合參數並執行
$argv = @(
    "main.py",
    "--provider", $Provider,
    "--in", $TargetsPath,
    "--out", $OutFile,
    "--timeout", $TimeoutSec,
    "--print"
)

try {
    & $PythonExe @argv
    $exitCode = $LASTEXITCODE
} catch {
    Fail ("執行失敗：{0}" -f $_.Exception.Message)
}

Write-Host ""
if ($exitCode -ne 0) {
    Fail "main.py 回傳非 0 結束碼（$exitCode）。請檢查螢幕輸出與設定。"
} else {
    Write-Host "查詢完成，結果已輸出至 $OutFile" -ForegroundColor Green
}
