# RBL 黑名單查詢工具 GUI 版本打包腳本 (PowerShell)
# 使用方式: .\build_gui.ps1

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "RBL 黑名單查詢工具 GUI 版本打包工具" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 檢查 Python 是否安裝
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✓ Python 版本: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 錯誤: 未找到 Python，請先安裝 Python 3.9 或更高版本" -ForegroundColor Red
    Read-Host "按 Enter 鍵退出"
    exit 1
}

# 檢查 PyInstaller 是否安裝
try {
    python -c "import PyInstaller" 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "安裝 PyInstaller..." -ForegroundColor Yellow
        pip install pyinstaller
        if ($LASTEXITCODE -ne 0) {
            Write-Host "❌ 錯誤: PyInstaller 安裝失敗" -ForegroundColor Red
            Read-Host "按 Enter 鍵退出"
            exit 1
        }
    }
    Write-Host "✓ PyInstaller 已安裝" -ForegroundColor Green
} catch {
    Write-Host "❌ 錯誤: 無法檢查 PyInstaller" -ForegroundColor Red
    Read-Host "按 Enter 鍵退出"
    exit 1
}

# 檢查必要檔案
if (-not (Test-Path "ui.py")) {
    Write-Host "❌ 錯誤: 找不到 ui.py 檔案" -ForegroundColor Red
    Read-Host "按 Enter 鍵退出"
    exit 1
}
Write-Host "✓ 檔案檢查通過" -ForegroundColor Green

Write-Host ""
Write-Host "開始打包..." -ForegroundColor Yellow
Write-Host ""

# 清理舊檔案
$itemsToClean = @("build", "dist", "*.spec")
foreach ($item in $itemsToClean) {
    if (Test-Path $item) {
        Remove-Item $item -Recurse -Force
        Write-Host "  已清理: $item" -ForegroundColor Gray
    }
}

# 執行打包
$pyinstallerArgs = @(
    "-m", "PyInstaller",
    "--onefile",
    "--windowed",
    "--name=RBL_Checker_GUI",
    "--add-data=GUI_使用說明.md;.",
    "--add-data=sample_domains.txt;.",
    "--hidden-import=tkinter",
    "--hidden-import=tkinter.ttk",
    "--hidden-import=tkinter.messagebox",
    "--hidden-import=tkinter.filedialog",
    "--hidden-import=tkinter.scrolledtext",
    "--hidden-import=requests",
    "--hidden-import=concurrent.futures",
    "--hidden-import=threading",
    "--exclude-module=matplotlib",
    "--exclude-module=numpy",
    "--exclude-module=pandas",
    "--exclude-module=PIL",
    "ui.py"
)

try {
    & python $pyinstallerArgs
    if ($LASTEXITCODE -ne 0) {
        throw "PyInstaller 執行失敗"
    }
} catch {
    Write-Host "❌ 錯誤: 打包失敗" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Read-Host "按 Enter 鍵退出"
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "打包完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan

# 檢查結果
$exePath = "dist\RBL_Checker_GUI.exe"
if (Test-Path $exePath) {
    $fileSize = (Get-Item $exePath).Length
    $sizeMB = [math]::Round($fileSize / 1MB, 1)
    
    Write-Host "檔案位置: $exePath" -ForegroundColor Green
    Write-Host "檔案大小: $sizeMB MB" -ForegroundColor Green
    Write-Host ""
    Write-Host "使用方式:" -ForegroundColor Yellow
    Write-Host "1. 直接執行 $exePath" -ForegroundColor White
    Write-Host "2. 首次使用請先設定 API 認證資訊" -ForegroundColor White
    Write-Host ""
    
    # 詢問是否立即執行
    $choice = Read-Host "是否立即執行程式？(y/n)"
    if ($choice -eq "y" -or $choice -eq "Y") {
        Start-Process $exePath
    }
} else {
    Write-Host "❌ 錯誤: 找不到生成的 EXE 檔案" -ForegroundColor Red
}

Write-Host ""
Read-Host "按 Enter 鍵退出"
