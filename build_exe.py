#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包腳本 - 將應用程式打包成 exe 檔案
使用 PyInstaller 進行打包
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_requirements():
    """檢查打包需求"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ 錯誤: 未安裝 PyInstaller")
        print("請執行: pip install pyinstaller")
        return False
    
    # 檢查主程式檔案
    if not os.path.exists('ui.py'):
        print("❌ 錯誤: 找不到 ui.py 檔案")
        return False

    # 檢查必要的依賴
    try:
        import requests
        print(f"✓ requests 已安裝")
    except ImportError:
        print("❌ 錯誤: 未安裝 requests")
        print("請執行: pip install requests")
        return False
    
    print("✓ 所有需求檢查通過")
    return True

def clean_build_dirs():
    """清理建置目錄"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"清理目錄: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理 .spec 檔案
    for spec_file in Path('.').glob('*.spec'):
        print(f"刪除 spec 檔案: {spec_file}")
        spec_file.unlink()

def create_pyinstaller_spec():
    """建立 PyInstaller 規格檔案"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
# RBL 黑名單查詢工具 GUI 版本打包規格檔案

block_cipher = None

a = Analysis(
    ['ui.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('README.md', '.'),
        ('requirements.txt', '.'),
        ('GUI_使用說明.md', '.'),
        ('sample_domains.txt', '.'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'tkinter.scrolledtext',
        'requests',
        'urllib3',
        'certifi',
        'charset_normalizer',
        'idna',
        'concurrent.futures',
        'threading',
        'dataclasses',
        'json',
        'csv',
        'os',
        'sys',
        're',
        'time',
        'datetime',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
        'jupyter',
        'notebook',
        'IPython',
        'pytest',
        'setuptools',
        'wheel',
        'pip',
        'distutils',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='RBL_Checker_GUI',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 如果有圖示檔案，可以在這裡指定
    version=None,  # 可以添加版本資訊檔案
)
'''
    
    with open('RBL_Checker_GUI.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)

    print("✓ 已建立 PyInstaller 規格檔案")

def build_executable():
    """建置可執行檔案"""
    print("開始建置可執行檔案...")
    
    # 使用 spec 檔案建置
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',
        'RBL_Checker_GUI.spec'
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ 建置成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 建置失敗: {e}")
        print(f"錯誤輸出: {e.stderr}")
        return False

def create_portable_package():
    """建立可攜式套件"""
    if not os.path.exists('dist/RBL_Checker_GUI.exe'):
        print("❌ 找不到建置的可執行檔案")
        return False

    # 建立可攜式目錄
    portable_dir = 'RBL_Checker_GUI_Portable'
    if os.path.exists(portable_dir):
        shutil.rmtree(portable_dir)

    os.makedirs(portable_dir)

    # 複製可執行檔案
    shutil.copy2('dist/RBL_Checker_GUI.exe', portable_dir)
    
    # 複製說明文件
    files_to_copy = ['README.md', 'requirements.txt', 'GUI_使用說明.md', 'sample_domains.txt']
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            shutil.copy2(file_name, portable_dir)
    
    # 建立範例設定檔
    sample_config = {
        "app": {
            "name": "RBL 黑名單查詢工具 GUI 版本",
            "version": "1.0.0",
            "window_width": 900,
            "window_height": 700
        },
        "api": {
            "hetrixtools": {
                "token": "請在此輸入您的 HetrixTools API Token"
            },
            "rbltracker": {
                "sid": "請在此輸入您的 RBLTracker SID",
                "token": "請在此輸入您的 RBLTracker Token"
            }
        },
        "query": {
            "timeout": 300,
            "concurrency": 4,
            "default_provider": "hetrixtools"
        }
    }
    
    import json
    with open(os.path.join(portable_dir, 'config.sample.json'), 'w', encoding='utf-8') as f:
        json.dump(sample_config, f, indent=4, ensure_ascii=False)
    
    # 建立使用說明
    usage_text = """RBL 黑名單查詢工具 GUI 版本 - 快速使用指南

🚀 快速開始:
1. 執行 RBL_Checker_GUI.exe 啟動程式
2. 點擊「API 設定」按鈕設定您的 API 認證資訊
3. 在「域名/IP」欄位輸入要查詢的目標
4. 選擇 API 提供者（HetrixTools 或 RBLTracker）
5. 點擊「開始查詢」按鈕
6. 查看結果並可匯出為 CSV 或 JSON 格式

📋 主要功能:
- ✅ 圖形化使用者介面，操作簡單直觀
- ✅ 支援 HetrixTools 和 RBLTracker 兩種 API
- ✅ 批次查詢多個域名或 IP 位址
- ✅ 從檔案匯入目標清單（TXT/CSV/TSV）
- ✅ 並行查詢，提高效率
- ✅ 詳細的查詢結果顯示
- ✅ 結果匯出功能（CSV/JSON）
- ✅ API 連線測試功能

⚙️ API 設定:
- HetrixTools: 需要 API Token
- RBLTracker: 需要 SID 和 Token
- 可在程式內直接設定，無需環境變數

📁 檔案說明:
- RBL_Checker_GUI.exe: 主程式
- GUI_使用說明.md: 詳細使用說明
- config.sample.json: 範例設定檔
- sample_domains.txt: 範例域名清單

🔗 取得 API 認證:
- HetrixTools: https://hetrixtools.com
- RBLTracker: https://rbltracker.com

⚠️ 注意事項:
- 需要網路連線才能進行 RBL 查詢
- 查詢速度取決於網路狀況和 API 回應時間
- 建議適當設定並行數和超時時間
- 請遵守 API 提供者的使用條款

📞 技術支援:
如有問題請參考 GUI_使用說明.md 檔案或 README.md
"""
    
    with open(os.path.join(portable_dir, '使用說明.txt'), 'w', encoding='utf-8') as f:
        f.write(usage_text)
    
    print(f"✓ 已建立可攜式套件: {portable_dir}")
    return True

def main():
    """主函數"""
    print("RBL Email Domains Status Checker 打包工具")
    print("=" * 50)
    
    # 檢查需求
    if not check_requirements():
        return 1
    
    # 清理建置目錄
    print("\n清理建置目錄...")
    clean_build_dirs()
    
    # 建立規格檔案
    print("\n建立 PyInstaller 規格檔案...")
    create_pyinstaller_spec()
    
    # 建置可執行檔案
    print("\n建置可執行檔案...")
    if not build_executable():
        return 1
    
    # 建立可攜式套件
    print("\n建立可攜式套件...")
    if not create_portable_package():
        return 1
    
    print("\n" + "=" * 50)
    print("✓ 打包完成！")
    print(f"可執行檔案位置: dist/RBL_Checker_GUI.exe")
    print(f"可攜式套件位置: RBL_Checker_GUI_Portable/")
    print("\n使用方式:")
    print("1. 直接執行 dist/RBL_Checker_GUI.exe")
    print("2. 或使用 RBL_Checker_GUI_Portable/ 目錄中的檔案")
    print("3. 首次使用請先設定 API 認證資訊")
    print("\n檔案大小資訊:")

    # 顯示檔案大小
    exe_path = 'dist/RBL_Checker_GUI.exe'
    if os.path.exists(exe_path):
        size_mb = os.path.getsize(exe_path) / (1024 * 1024)
        print(f"EXE 檔案大小: {size_mb:.1f} MB")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
