#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包腳本 - 將應用程式打包成 exe 檔案
使用 PyInstaller 進行打包
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_requirements():
    """檢查打包需求"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ 錯誤: 未安裝 PyInstaller")
        print("請執行: pip install pyinstaller")
        return False
    
    # 檢查主程式檔案
    if not os.path.exists('main.py'):
        print("❌ 錯誤: 找不到 main.py 檔案")
        return False
    
    print("✓ 所有需求檢查通過")
    return True

def clean_build_dirs():
    """清理建置目錄"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"清理目錄: {dir_name}")
            shutil.rmtree(dir_name)
    
    # 清理 .spec 檔案
    for spec_file in Path('.').glob('*.spec'):
        print(f"刪除 spec 檔案: {spec_file}")
        spec_file.unlink()

def create_pyinstaller_spec():
    """建立 PyInstaller 規格檔案"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('README.md', '.'),
        ('requirements.txt', '.'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'requests',
        'beautifulsoup4',
        'lxml',
        'urllib3',
        'certifi',
        'charset_normalizer',
        'soupsieve',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
        'jupyter',
        'notebook',
        'IPython',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='RBL_Checker',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 如果有圖示檔案，可以在這裡指定
)
'''
    
    with open('RBL_Checker.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 已建立 PyInstaller 規格檔案")

def build_executable():
    """建置可執行檔案"""
    print("開始建置可執行檔案...")
    
    # 使用 spec 檔案建置
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',
        'RBL_Checker.spec'
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ 建置成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 建置失敗: {e}")
        print(f"錯誤輸出: {e.stderr}")
        return False

def create_portable_package():
    """建立可攜式套件"""
    if not os.path.exists('dist/RBL_Checker.exe'):
        print("❌ 找不到建置的可執行檔案")
        return False
    
    # 建立可攜式目錄
    portable_dir = 'RBL_Checker_Portable'
    if os.path.exists(portable_dir):
        shutil.rmtree(portable_dir)
    
    os.makedirs(portable_dir)
    
    # 複製可執行檔案
    shutil.copy2('dist/RBL_Checker.exe', portable_dir)
    
    # 複製說明文件
    files_to_copy = ['README.md', 'requirements.txt']
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            shutil.copy2(file_name, portable_dir)
    
    # 建立範例設定檔
    sample_config = {
        "app": {
            "name": "RBL Email Domains Status Checker",
            "version": "1.0.0",
            "window_width": 800,
            "window_height": 600
        },
        "rbl": {
            "timeout": 30,
            "max_workers": 5
        }
    }
    
    import json
    with open(os.path.join(portable_dir, 'config.sample.json'), 'w', encoding='utf-8') as f:
        json.dump(sample_config, f, indent=4, ensure_ascii=False)
    
    # 建立使用說明
    usage_text = """RBL Email Domains Status Checker 使用說明

1. 執行 RBL_Checker.exe 啟動程式
2. 在輸入框中輸入要檢查的域名
3. 點擊「檢查 RBL 狀態」按鈕開始檢查
4. 查看檢查結果
5. 可以使用「批量檢查」功能檢查多個域名
6. 使用「匯出結果」功能儲存檢查結果

設定檔案:
- 可以建立 config.json 檔案來自訂設定
- 參考 config.sample.json 範例檔案

注意事項:
- 需要網路連線才能進行 RBL 檢查
- 檢查速度取決於網路狀況和 RBL 服務回應時間
- 建議不要同時檢查過多域名以避免被限制存取

技術支援:
如有問題請參考 README.md 檔案或聯絡開發者
"""
    
    with open(os.path.join(portable_dir, '使用說明.txt'), 'w', encoding='utf-8') as f:
        f.write(usage_text)
    
    print(f"✓ 已建立可攜式套件: {portable_dir}")
    return True

def main():
    """主函數"""
    print("RBL Email Domains Status Checker 打包工具")
    print("=" * 50)
    
    # 檢查需求
    if not check_requirements():
        return 1
    
    # 清理建置目錄
    print("\n清理建置目錄...")
    clean_build_dirs()
    
    # 建立規格檔案
    print("\n建立 PyInstaller 規格檔案...")
    create_pyinstaller_spec()
    
    # 建置可執行檔案
    print("\n建置可執行檔案...")
    if not build_executable():
        return 1
    
    # 建立可攜式套件
    print("\n建立可攜式套件...")
    if not create_portable_package():
        return 1
    
    print("\n" + "=" * 50)
    print("✓ 打包完成！")
    print(f"可執行檔案位置: dist/RBL_Checker.exe")
    print(f"可攜式套件位置: RBL_Checker_Portable/")
    print("\n使用方式:")
    print("1. 直接執行 dist/RBL_Checker.exe")
    print("2. 或使用 RBL_Checker_Portable/ 目錄中的檔案")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
