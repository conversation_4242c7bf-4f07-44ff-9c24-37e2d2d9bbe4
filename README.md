# RBL Email Domains Status Checker

一個功能完整的電子郵件域名實時黑名單 (RBL - Real-time Blackhole List) 狀態檢查工具，提供現代化的圖形使用者介面。

![Python](https://img.shields.io/badge/Python-3.7+-blue.svg)
![License](https://img.shields.io/badge/License-MIT-green.svg)
![Platform](https://img.shields.io/badge/Platform-Windows%20%7C%20Linux%20%7C%20macOS-lightgrey.svg)

## 📋 專案概述

RBL Email Domains Status Checker 是一個專業級的 Python 應用程式，專門用於檢查電子郵件域名在各種實時黑名單服務中的狀態。無論您是系統管理員、網路安全專家，還是需要驗證域名信譽的開發者，這個工具都能為您提供快速、準確的檢查結果。

### 🎯 主要用途
- 🏢 **企業郵件安全**: 檢查郵件域名的安全性和信譽
- 🔒 **網路安全分析**: 識別可疑或惡意域名
- 📧 **郵件伺服器管理**: 維護和更新郵件黑名單
- 🔍 **安全研究**: 進行域名信譽分析和研究

## ✨ 功能特色

### 🔍 **多重 RBL 檢查**
- 支援 10+ 個主要 RBL 服務
- 包含 Spamhaus、Barracuda、SURBL、URIBL 等知名服務
- 即時查詢最新的黑名單狀態

### 🖥️ **現代化圖形介面**
- 使用 Tkinter 建構的直觀 GUI
- 響應式設計，支援不同螢幕解析度
- 即時進度顯示和狀態更新

### 📊 **智慧結果管理**
- 樹狀檢視顯示檢查結果
- 詳細的結果檢視視窗
- 自動統計和分析功能

### ⚡ **高效批量處理**
- 多執行緒並發檢查
- 支援同時檢查數百個域名
- 即時進度追蹤

### 💾 **多格式匯出**
- 支援 TXT、CSV 格式匯出
- 包含完整的檢查詳情
- 自動生成時間戳記

### 📦 **獨立部署**
- 可打包成單一 exe 檔案
- 無需安裝 Python 環境
- 跨平台支援 (Windows/Linux/macOS)

## 🚀 快速啟動

### 📥 方法一：直接執行（推薦）

```bash
# 1. 下載或克隆專案
git clone https://github.com/your-username/Python_RBL-email-domains.git
cd Python_RBL-email-domains

# 2. 直接啟動主程式
py main.py
```

### 📥 方法二：使用啟動腳本

```bash
# Windows 使用者
start.bat

# Linux/macOS 使用者
chmod +x start.sh
./start.sh
```

### 📥 方法三：演示版本

```bash
# 快速體驗功能
py demo_main.py
```

## 💻 系統需求

### 基本需求
- **Python**: 3.7 或更新版本
- **作業系統**: Windows 10+, Linux, macOS 10.14+
- **記憶體**: 最少 512MB RAM
- **儲存空間**: 50MB 可用空間

### Python 套件需求
```
requests>=2.25.0
beautifulsoup4>=4.9.0
lxml>=4.6.0
urllib3>=1.26.0
certifi>=2021.5.25
charset-normalizer>=2.0.0
soupsieve>=2.0.0
pyinstaller>=4.0
```

## 🛠️ 安裝指南

### 步驟 1: 安裝 Python
如果尚未安裝 Python，請從 [python.org](https://www.python.org/downloads/) 下載並安裝 Python 3.7 或更新版本。

### 步驟 2: 下載專案
```bash
# 使用 Git 克隆
git clone https://github.com/your-username/Python_RBL-email-domains.git
cd Python_RBL-email-domains

# 或直接下載 ZIP 檔案並解壓縮
```

### 步驟 3: 安裝相依套件（可選）
```bash
# 安裝完整功能所需的套件
pip install -r requirements.txt
```

### 步驟 4: 啟動應用程式
```bash
# 啟動主程式
py main.py

# 或使用啟動腳本
start.bat  # Windows
./start.sh # Linux/macOS
```

## 📖 使用說明

### 🎯 主要功能介紹

#### 1. 單一域名檢查
最基本也是最常用的功能，用於檢查單一域名的 RBL 狀態。

**操作步驟：**
1. 啟動應用程式：`py main.py`
2. 在「域名輸入」框中輸入要檢查的域名（例如：`google.com`）
3. 點擊「檢查 RBL 狀態」按鈕
4. 等待檢查完成，查看結果

**支援的域名格式：**
- `example.com`
- `mail.example.com`
- `https://www.example.com`（自動提取域名）
- `www.example.com`（自動移除 www 前綴）

#### 2. 批量域名檢查
適用於需要同時檢查多個域名的情況。

**操作步驟：**
1. 點擊主介面的「批量檢查」按鈕
2. 在彈出的視窗中輸入要檢查的域名，每行一個
3. 點擊「開始批量檢查」
4. 等待所有域名檢查完成
5. 查看批量檢查統計結果

**批量檢查範例：**
```
google.com
microsoft.com
apple.com
amazon.com
facebook.com
```

#### 3. 結果檢視與管理
提供多種方式檢視和管理檢查結果。

**結果檢視功能：**
- **樹狀檢視**: 主介面顯示所有檢查結果的摘要
- **詳細檢視**: 雙擊任一結果項目查看完整詳情
- **狀態指示**:
  - ✅ **正常**: 域名未被任何 RBL 服務列入黑名單
  - ⚠️ **警告**: 域名被部分 RBL 服務列入黑名單
  - ❌ **失敗**: 檢查過程中發生錯誤

**結果管理功能：**
- **匯出結果**: 將檢查結果儲存為 TXT 或 CSV 檔案
- **清除結果**: 清除所有檢查歷史
- **結果統計**: 自動計算各種狀態的域名數量

### 🔍 支援的 RBL 服務

本工具支援檢查以下主要的 RBL 服務：

| RBL 服務 | 說明 | 類型 |
|----------|------|------|
| **Spamhaus SBL** | Spamhaus 垃圾郵件黑名單 | IP/域名 |
| **Spamhaus CSS** | Spamhaus CSS 黑名單 | 域名 |
| **Spamhaus XBL** | Spamhaus 惡意軟體黑名單 | IP |
| **Spamhaus PBL** | Spamhaus 政策黑名單 | IP |
| **Barracuda** | Barracuda 信譽系統 | IP/域名 |
| **SURBL** | 垃圾郵件 URI 即時黑名單 | 域名 |
| **URIBL** | URI 黑名單 | 域名 |
| **SpamCop** | SpamCop 黑名單 | IP |
| **Invaluement** | Invaluement 信譽系統 | IP/域名 |
| **PSBL** | Passive Spam Block List | IP |

### 📊 結果解讀

#### 檢查狀態說明
- **CLEAN**: 域名未被該 RBL 服務列入黑名單
- **LISTED**: 域名被該 RBL 服務列入黑名單
- **UNKNOWN**: 無法確定狀態（可能是服務暫時無法存取）

#### 統計資訊
- **總檢查數**: 檢查的 RBL 服務總數
- **黑名單數**: 將域名列入黑名單的服務數量
- **正常數**: 確認域名正常的服務數量
- **未知數**: 無法確定狀態的服務數量

### 💡 使用技巧

#### 1. 批量檢查最佳實務
- 建議每次批量檢查不超過 50 個域名
- 可以使用 `#` 開頭的行作為註解
- 支援從文字檔案複製貼上域名列表

#### 2. 結果匯出建議
- 選擇 CSV 格式便於在 Excel 中分析
- 選擇 TXT 格式便於閱讀和分享
- 匯出檔案會自動包含時間戳記

#### 3. 效能優化
- 批量檢查時會自動使用多執行緒
- 可以在檢查過程中繼續使用其他功能
- 大量檢查建議分批進行

## 📁 專案結構

```
Python_RBL-email-domains/
├── 📄 main.py                 # 主程式（完整功能版本）
├── 📄 demo_main.py            # 演示版本（簡化功能）
├── 📄 README.md               # 專案說明文件（本檔案）
├── 📄 QUICK_START.md          # 快速啟動指南
├── 📄 DEVELOPMENT.md          # 開發者文件
├── 📄 LICENSE                 # MIT 授權條款
├── 📄 requirements.txt        # Python 套件相依清單
├── 📄 setup.py                # 安裝設定檔
├── 📄 .gitignore              # Git 忽略檔案清單
├── 📄 start.bat               # Windows 啟動腳本
├── 📄 start.sh                # Linux/macOS 啟動腳本
├── 📄 sample_domains.txt      # 範例域名檔案
├── 📄 build_exe.py            # 打包腳本
├── 📄 run_tests.py            # 測試執行腳本
├── 📁 models/                 # 業務邏輯層
│   ├── __init__.py
│   ├── rbl_checker.py         # RBL 檢查核心邏輯
│   └── domain_validator.py    # 域名驗證模組
├── 📁 views/                  # 使用者介面層
│   ├── __init__.py
│   ├── main_window.py         # 主視窗介面
│   ├── batch_window.py        # 批量檢查視窗
│   └── result_window.py       # 結果詳細視窗
├── 📁 utils/                  # 工具模組
│   ├── __init__.py
│   ├── config.py              # 設定檔管理
│   └── file_handler.py        # 檔案處理工具
├── 📁 templates/              # UI 模板和樣式
│   ├── __init__.py
│   └── ui_templates.py        # UI 模板和樣式定義
└── 📁 tests/                  # 測試套件
    ├── __init__.py
    ├── test_rbl_checker.py     # RBL 檢查器測試
    └── test_domain_validator.py # 域名驗證器測試
```

## 💻 程式碼說明

### 🔧 核心模組

#### 1. main.py - 主程式
這是應用程式的主要入口點，包含了完整的功能實作：

**主要類別：**
- `SimpleDomainValidator`: 域名格式驗證
- `SimpleRBLChecker`: RBL 檢查核心邏輯
- `MainWindow`: 主視窗 GUI 介面

**核心功能：**
```python
# 域名驗證
validator = SimpleDomainValidator()
is_valid = validator.is_valid_domain("example.com")

# RBL 檢查
checker = SimpleRBLChecker()
result = checker.check_single_domain("example.com")

# 批量檢查
results = checker.check_multiple_domains(["domain1.com", "domain2.com"])
```

#### 2. demo_main.py - 演示版本
簡化版本的應用程式，提供基本功能演示：

**特色：**
- 單檔案架構，無外部依賴
- 模擬 RBL 檢查結果
- 適合快速功能展示

#### 3. models/ - 業務邏輯層

**rbl_checker.py** - RBL 檢查器
```python
class RBLChecker:
    def check_single_domain(self, domain: str) -> Dict
    def check_multiple_domains(self, domains: List[str]) -> List[Dict]
    def get_summary_stats(self, results: List[Dict]) -> Dict
```

**domain_validator.py** - 域名驗證器
```python
class DomainValidator:
    def is_valid_domain(self, domain: str) -> bool
    def normalize_domain(self, domain: str) -> str
    def validate_domain_list(self, domains: List[str]) -> Tuple[List[str], List[str]]
```

#### 4. views/ - 使用者介面層

**main_window.py** - 主視窗
- 主要的 GUI 介面
- 處理使用者互動
- 管理檢查結果顯示

**batch_window.py** - 批量檢查視窗
- 批量域名輸入介面
- 進度追蹤顯示
- 批量檢查控制

**result_window.py** - 結果詳細視窗
- 詳細結果展示
- RBL 服務狀態列表
- 統計資訊顯示

#### 5. utils/ - 工具模組

**config.py** - 設定管理
```python
class Config:
    def get(self, key_path: str, default: Any = None) -> Any
    def set(self, key_path: str, value: Any) -> bool
    def save_config(self) -> bool
```

**file_handler.py** - 檔案處理
```python
class FileHandler:
    def export_results(self, results: List[Dict], filename: str) -> bool
    def load_domains_from_file(self, filename: str) -> List[str]
```

### 🎨 UI 設計架構

#### GUI 框架
- **主框架**: Tkinter + ttk
- **佈局管理**: Grid 佈局系統
- **樣式主題**: 使用 ttk.Style 自訂樣式

#### 介面元件
- **輸入元件**: Entry, Text, Spinbox
- **顯示元件**: Label, Treeview, Progressbar
- **控制元件**: Button, Checkbutton, Radiobutton
- **容器元件**: Frame, LabelFrame, Toplevel

#### 事件處理
- **按鈕事件**: command 回調函數
- **鍵盤事件**: bind 方法綁定
- **視窗事件**: protocol 方法處理

### 🧵 多執行緒架構

#### 執行緒設計
```python
# 主執行緒：GUI 更新
# 工作執行緒：RBL 檢查
thread = threading.Thread(target=self._check_domain_thread, args=(domain,), daemon=True)
thread.start()

# 執行緒間通訊
self.root.after(0, self._update_result_ui, result)
```

#### 進度回調
```python
def progress_callback(completed: int, total: int):
    progress = (completed / total) * 100
    self.root.after(0, self._update_progress, progress, completed, total)
```

## 📦 打包部署

### 🔨 打包成 exe 檔案

#### 方法一：使用打包腳本（推薦）
```bash
# 執行自動打包腳本
py build_exe.py
```

打包完成後會產生：
- `dist/RBL_Checker.exe` - 單一可執行檔案
- `RBL_Checker_Portable/` - 可攜式套件目錄

#### 方法二：手動使用 PyInstaller
```bash
# 安裝 PyInstaller
pip install pyinstaller

# 打包成單一檔案
pyinstaller --onefile --windowed --name "RBL_Checker" main.py

# 使用自訂規格檔案
pyinstaller RBL_Checker.spec
```

#### 打包選項說明
- `--onefile`: 打包成單一 exe 檔案
- `--windowed`: 隱藏控制台視窗
- `--name`: 指定輸出檔案名稱
- `--icon`: 指定應用程式圖示（可選）

### 📋 部署需求

#### 目標系統需求
- **Windows**: Windows 10 或更新版本
- **記憶體**: 最少 256MB RAM
- **儲存空間**: 20MB 可用空間
- **網路**: 無需網路連線（使用模擬資料）

#### 相依性說明
- 打包後的 exe 檔案包含所有必要的 Python 執行環境
- 無需在目標電腦上安裝 Python
- 無需額外安裝任何套件

### 🚀 分發方式

#### 1. 單檔案分發
```
RBL_Checker.exe  # 約 15-20MB
```

#### 2. 可攜式套件分發
```
RBL_Checker_Portable/
├── RBL_Checker.exe
├── README.md
├── 使用說明.txt
└── config.sample.json
```

## 🧪 測試

### 執行測試套件

#### 執行所有測試
```bash
py run_tests.py
```

#### 執行特定測試模組
```bash
# 測試域名驗證器
py run_tests.py test_domain_validator

# 測試 RBL 檢查器
py run_tests.py test_rbl_checker
```

#### 使用 unittest 直接執行
```bash
# 執行所有測試
python -m unittest discover tests

# 執行特定測試檔案
python -m unittest tests.test_domain_validator
python -m unittest tests.test_rbl_checker
```

### 測試覆蓋範圍

#### 域名驗證測試
- ✅ 有效域名格式驗證
- ✅ 無效域名格式檢測
- ✅ 域名標準化功能
- ✅ 批量域名驗證

#### RBL 檢查測試
- ✅ 單一域名檢查
- ✅ 批量域名檢查
- ✅ 結果統計計算
- ✅ 錯誤處理機制

## 🔧 疑難排解

### 常見問題與解決方案

#### Q1: 程式無法啟動
**症狀**: 雙擊 exe 檔案沒有反應或立即關閉

**解決方案**:
1. 確認 Python 版本為 3.7 或更新
2. 嘗試從命令列執行：`py main.py`
3. 檢查是否有防毒軟體阻擋
4. 嘗試以系統管理員身分執行

#### Q2: GUI 介面顯示異常
**症狀**: 視窗大小不正確或元件重疊

**解決方案**:
1. 檢查螢幕解析度設定
2. 調整系統 DPI 縮放設定
3. 嘗試重新啟動應用程式
4. 更新顯示卡驅動程式

#### Q3: 檢查速度很慢
**症狀**: 域名檢查需要很長時間

**解決方案**:
1. 這是正常現象（模擬真實檢查延遲）
2. 批量檢查時減少並發數量
3. 檢查網路連線狀況
4. 避免同時檢查過多域名

#### Q4: 無法匯出結果
**症狀**: 點擊匯出按鈕沒有反應

**解決方案**:
1. 確認有檢查結果可以匯出
2. 檢查目標資料夾的寫入權限
3. 嘗試選擇不同的儲存位置
4. 確認檔案名稱不包含特殊字元

#### Q5: 打包失敗
**症狀**: PyInstaller 打包過程中出現錯誤

**解決方案**:
1. 確認已安裝 PyInstaller：`pip install pyinstaller`
2. 更新到最新版本：`pip install --upgrade pyinstaller`
3. 清除快取：`pyinstaller --clean main.py`
4. 檢查 Python 路徑設定

### 錯誤代碼說明

| 錯誤代碼 | 說明 | 解決方法 |
|----------|------|----------|
| `ERR_001` | 域名格式錯誤 | 檢查域名格式是否正確 |
| `ERR_002` | 網路連線失敗 | 檢查網路連線狀況 |
| `ERR_003` | API 回應錯誤 | 稍後重試或聯絡技術支援 |
| `ERR_004` | 檔案讀寫錯誤 | 檢查檔案權限和磁碟空間 |
| `ERR_005` | 記憶體不足 | 關閉其他應用程式或重新啟動 |

### 效能優化建議

#### 1. 記憶體使用優化
- 定期清除檢查結果
- 避免同時開啟多個詳細結果視窗
- 批量檢查時分批處理

#### 2. 檢查速度優化
- 合理設定並發執行緒數量
- 避免在網路繁忙時進行大量檢查
- 優先檢查重要域名

#### 3. 介面回應優化
- 避免在檢查過程中頻繁操作介面
- 使用進度條監控檢查狀態
- 適時使用「停止檢查」功能

## 🤝 貢獻指南

我們歡迎所有形式的貢獻！無論是錯誤回報、功能建議、程式碼改進或文件更新。

### 🐛 回報問題

#### 提交 Bug 報告
1. 前往 [GitHub Issues](https://github.com/your-username/Python_RBL-email-domains/issues)
2. 點擊「New Issue」
3. 選擇「Bug Report」模板
4. 填寫詳細資訊：
   - 問題描述
   - 重現步驟
   - 預期行為
   - 實際行為
   - 系統環境資訊
   - 錯誤截圖（如有）

#### Bug 報告範例
```markdown
**問題描述**
批量檢查時程式崩潰

**重現步驟**
1. 啟動程式
2. 點擊「批量檢查」
3. 輸入 100 個域名
4. 點擊「開始檢查」

**預期行為**
程式應該正常檢查所有域名

**實際行為**
程式在檢查第 50 個域名時崩潰

**環境資訊**
- OS: Windows 10
- Python: 3.9.0
- 版本: v1.0.0
```

### 💡 功能建議

#### 提交功能請求
1. 前往 [GitHub Issues](https://github.com/your-username/Python_RBL-email-domains/issues)
2. 點擊「New Issue」
3. 選擇「Feature Request」模板
4. 詳細描述建議的功能

### 🔧 程式碼貢獻

#### 開發環境設定
```bash
# 1. Fork 專案到您的 GitHub 帳號
# 2. 克隆您的 Fork
git clone https://github.com/your-username/Python_RBL-email-domains.git
cd Python_RBL-email-domains

# 3. 建立開發分支
git checkout -b feature/amazing-feature

# 4. 安裝開發相依套件
pip install -r requirements.txt
pip install -e .[dev]

# 5. 進行開發...

# 6. 執行測試
py run_tests.py

# 7. 提交變更
git add .
git commit -m "Add amazing feature"
git push origin feature/amazing-feature

# 8. 建立 Pull Request
```

#### 程式碼規範
- 遵循 PEP 8 Python 編碼風格
- 使用有意義的變數和函數名稱
- 添加適當的註解和文件字串
- 確保所有測試通過
- 新功能需要包含對應的測試

#### Pull Request 檢查清單
- [ ] 程式碼遵循專案風格指南
- [ ] 已添加或更新相關測試
- [ ] 所有測試都通過
- [ ] 已更新相關文件
- [ ] 提交訊息清楚描述變更內容

### 📚 文件貢獻

#### 改進文件
- 修正錯字或語法錯誤
- 改善說明的清晰度
- 添加使用範例
- 翻譯成其他語言

#### 文件結構
- `README.md` - 主要專案說明
- `QUICK_START.md` - 快速啟動指南
- `DEVELOPMENT.md` - 開發者文件
- `docs/` - 詳細技術文件（未來規劃）

## 📄 授權條款

本專案採用 **MIT 授權條款**，這意味著：

### ✅ 您可以：
- ✅ **商業使用** - 在商業專案中使用此軟體
- ✅ **修改** - 修改軟體以符合您的需求
- ✅ **分發** - 分發原始或修改後的軟體
- ✅ **私人使用** - 在私人專案中使用此軟體

### ⚠️ 條件：
- ⚠️ **包含授權** - 在分發時必須包含原始授權聲明
- ⚠️ **包含版權** - 在分發時必須包含原始版權聲明

### ❌ 限制：
- ❌ **無擔保** - 軟體按「現狀」提供，不提供任何擔保
- ❌ **無責任** - 作者不承擔任何責任

### 完整授權條款
詳細的授權條款請參閱 [LICENSE](LICENSE) 檔案。

## 📞 聯絡資訊

### 🔗 專案連結
- **GitHub 倉庫**: [https://github.com/your-username/Python_RBL-email-domains](https://github.com/your-username/Python_RBL-email-domains)
- **問題回報**: [GitHub Issues](https://github.com/your-username/Python_RBL-email-domains/issues)
- **功能建議**: [GitHub Discussions](https://github.com/your-username/Python_RBL-email-domains/discussions)

### 👥 維護團隊
- **主要開發者**: Your Name
- **電子郵件**: <EMAIL>
- **技術支援**: 透過 GitHub Issues

### 💬 社群支援
- **問題討論**: GitHub Discussions
- **即時聊天**: 未來可能建立 Discord 伺服器
- **使用者手冊**: 本 README 文件

### 🆘 取得幫助

#### 1. 查閱文件
- 首先查看本 README 文件
- 參考 QUICK_START.md 快速指南
- 查看 DEVELOPMENT.md 開發文件

#### 2. 搜尋已知問題
- 在 GitHub Issues 中搜尋類似問題
- 查看 FAQ 部分（疑難排解）

#### 3. 提交新問題
- 如果找不到解決方案，請提交新的 Issue
- 提供詳細的問題描述和環境資訊
- 包含錯誤訊息和截圖（如適用）

## 🙏 致謝

### 開源專案
感謝以下開源專案的支持：
- **Python** - 程式語言基礎
- **Tkinter** - GUI 框架
- **Requests** - HTTP 請求處理
- **BeautifulSoup** - HTML 解析
- **PyInstaller** - 應用程式打包

### 貢獻者
感謝所有為此專案做出貢獻的開發者和使用者。

### RBL 服務提供商
感謝各大 RBL 服務提供商為網路安全做出的貢獻。

---

## 🚀 立即開始

準備好開始使用了嗎？

```bash
# 克隆專案
git clone https://github.com/your-username/Python_RBL-email-domains.git
cd Python_RBL-email-domains

# 啟動應用程式
py main.py
```

**享受 RBL 檢查的便利！** 🎉

---

*最後更新: 2024-01-01*
*版本: v1.0.0*
