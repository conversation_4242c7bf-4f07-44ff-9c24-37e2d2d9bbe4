#!/bin/bash

# RBL Email Domains Status Checker 啟動腳本 (Linux/macOS)

echo "========================================"
echo "RBL Email Domains Status Checker"
echo "========================================"
echo

# 檢查 Python 是否已安裝
if ! command -v python3 &> /dev/null; then
    echo "錯誤: 未找到 Python3"
    echo "請先安裝 Python 3.7 或更新版本"
    exit 1
fi

echo "檢查 Python 版本..."
python3 --version

# 檢查虛擬環境
if [ ! -d "venv" ]; then
    echo
    echo "建立虛擬環境..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "錯誤: 無法建立虛擬環境"
        exit 1
    fi
fi

# 啟動虛擬環境
echo
echo "啟動虛擬環境..."
source venv/bin/activate

# 安裝相依套件
echo
echo "檢查並安裝相依套件..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "錯誤: 無法安裝相依套件"
    exit 1
fi

# 啟動應用程式
echo
echo "啟動 RBL Email Domains Status Checker..."
echo
python main.py

# 檢查程式結束狀態
if [ $? -ne 0 ]; then
    echo
    echo "程式異常結束，錯誤代碼: $?"
    read -p "按 Enter 鍵繼續..."
fi

echo
echo "程式已結束"
