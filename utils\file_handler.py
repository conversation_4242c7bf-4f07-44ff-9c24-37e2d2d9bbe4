#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檔案處理工具模組
提供結果匯出和檔案操作功能
"""

import csv
import json
import os
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
import xml.etree.ElementTree as ET

class FileHandler:
    """檔案處理器類別"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def export_results(self, results: List[Dict], filename: str, 
                      format_type: Optional[str] = None) -> bool:
        """匯出檢查結果到檔案"""
        try:
            # 自動偵測格式
            if format_type is None:
                format_type = self.detect_format_from_filename(filename)
            
            # 根據格式匯出
            if format_type.lower() == 'csv':
                return self.export_to_csv(results, filename)
            elif format_type.lower() == 'json':
                return self.export_to_json(results, filename)
            elif format_type.lower() == 'xml':
                return self.export_to_xml(results, filename)
            elif format_type.lower() == 'txt':
                return self.export_to_txt(results, filename)
            else:
                # 預設使用 CSV 格式
                return self.export_to_csv(results, filename)
                
        except Exception as e:
            self.logger.error(f"匯出結果失敗: {str(e)}")
            return False
    
    def detect_format_from_filename(self, filename: str) -> str:
        """從檔案名稱偵測格式"""
        ext = os.path.splitext(filename)[1].lower()
        
        format_map = {
            '.csv': 'csv',
            '.json': 'json',
            '.xml': 'xml',
            '.txt': 'txt',
            '.tsv': 'csv'  # 使用 CSV 處理器但用 tab 分隔
        }
        
        return format_map.get(ext, 'csv')
    
    def export_to_csv(self, results: List[Dict], filename: str) -> bool:
        """匯出到 CSV 格式"""
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                # 準備 CSV 標題
                fieldnames = [
                    'domain', 'status', 'timestamp', 'message',
                    'total_rbl_checks', 'listed_count', 'clean_count', 'unknown_count'
                ]
                
                # 收集所有 RBL 服務名稱
                rbl_services = set()
                for result in results:
                    if result.get('results'):
                        rbl_services.update(result['results'].keys())
                
                # 為每個 RBL 服務添加欄位
                for service in sorted(rbl_services):
                    fieldnames.extend([
                        f'{service}_status',
                        f'{service}_details'
                    ])
                
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                # 寫入資料
                for result in self.prepare_csv_data(results, rbl_services):
                    writer.writerow(result)
                
                self.logger.info(f"成功匯出 {len(results)} 筆結果到 CSV: {filename}")
                return True
                
        except Exception as e:
            self.logger.error(f"匯出 CSV 失敗: {str(e)}")
            return False
    
    def prepare_csv_data(self, results: List[Dict], rbl_services: set) -> List[Dict]:
        """準備 CSV 資料"""
        csv_data = []
        
        for result in results:
            row = {
                'domain': result.get('domain', ''),
                'status': result.get('status', ''),
                'timestamp': result.get('timestamp', ''),
                'message': result.get('message', '')
            }
            
            # 統計 RBL 結果
            rbl_results = result.get('results', {})
            listed_count = sum(1 for r in rbl_results.values() if r.get('status') == 'LISTED')
            clean_count = sum(1 for r in rbl_results.values() if r.get('status') == 'CLEAN')
            unknown_count = len(rbl_results) - listed_count - clean_count
            
            row.update({
                'total_rbl_checks': len(rbl_results),
                'listed_count': listed_count,
                'clean_count': clean_count,
                'unknown_count': unknown_count
            })
            
            # 添加每個 RBL 服務的詳細結果
            for service in sorted(rbl_services):
                if service in rbl_results:
                    rbl_data = rbl_results[service]
                    row[f'{service}_status'] = rbl_data.get('status', 'UNKNOWN')
                    row[f'{service}_details'] = rbl_data.get('details', '')
                else:
                    row[f'{service}_status'] = 'NOT_CHECKED'
                    row[f'{service}_details'] = ''
            
            csv_data.append(row)
        
        return csv_data
    
    def export_to_json(self, results: List[Dict], filename: str) -> bool:
        """匯出到 JSON 格式"""
        try:
            export_data = {
                'export_info': {
                    'timestamp': datetime.now().isoformat(),
                    'total_results': len(results),
                    'format': 'json',
                    'version': '1.0'
                },
                'results': results
            }
            
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(export_data, jsonfile, indent=2, ensure_ascii=False)
            
            self.logger.info(f"成功匯出 {len(results)} 筆結果到 JSON: {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"匯出 JSON 失敗: {str(e)}")
            return False
    
    def export_to_xml(self, results: List[Dict], filename: str) -> bool:
        """匯出到 XML 格式"""
        try:
            # 建立根元素
            root = ET.Element('rbl_check_results')
            
            # 添加匯出資訊
            export_info = ET.SubElement(root, 'export_info')
            ET.SubElement(export_info, 'timestamp').text = datetime.now().isoformat()
            ET.SubElement(export_info, 'total_results').text = str(len(results))
            ET.SubElement(export_info, 'format').text = 'xml'
            ET.SubElement(export_info, 'version').text = '1.0'
            
            # 添加結果
            results_element = ET.SubElement(root, 'results')
            
            for result in results:
                result_element = ET.SubElement(results_element, 'result')
                
                # 基本資訊
                ET.SubElement(result_element, 'domain').text = result.get('domain', '')
                ET.SubElement(result_element, 'status').text = result.get('status', '')
                ET.SubElement(result_element, 'timestamp').text = result.get('timestamp', '')
                ET.SubElement(result_element, 'message').text = result.get('message', '')
                
                # RBL 檢查結果
                rbl_results = result.get('results', {})
                if rbl_results:
                    rbl_element = ET.SubElement(result_element, 'rbl_checks')
                    
                    for service, rbl_data in rbl_results.items():
                        service_element = ET.SubElement(rbl_element, 'rbl_service')
                        service_element.set('name', service)
                        
                        ET.SubElement(service_element, 'status').text = rbl_data.get('status', '')
                        ET.SubElement(service_element, 'details').text = rbl_data.get('details', '')
                        ET.SubElement(service_element, 'raw_status').text = rbl_data.get('raw_status', '')
            
            # 寫入檔案
            tree = ET.ElementTree(root)
            tree.write(filename, encoding='utf-8', xml_declaration=True)
            
            self.logger.info(f"成功匯出 {len(results)} 筆結果到 XML: {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"匯出 XML 失敗: {str(e)}")
            return False
    
    def export_to_txt(self, results: List[Dict], filename: str) -> bool:
        """匯出到純文字格式"""
        try:
            with open(filename, 'w', encoding='utf-8') as txtfile:
                # 寫入標題
                txtfile.write("RBL Email Domains Status Check Results\n")
                txtfile.write("=" * 50 + "\n")
                txtfile.write(f"Export Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                txtfile.write(f"Total Results: {len(results)}\n\n")
                
                # 寫入每個結果
                for i, result in enumerate(results, 1):
                    txtfile.write(f"Result #{i}\n")
                    txtfile.write("-" * 30 + "\n")
                    txtfile.write(f"Domain: {result.get('domain', 'Unknown')}\n")
                    txtfile.write(f"Status: {result.get('status', 'Unknown')}\n")
                    txtfile.write(f"Check Time: {result.get('timestamp', 'Unknown')}\n")
                    txtfile.write(f"Message: {result.get('message', 'No message')}\n")
                    
                    # RBL 檢查結果
                    rbl_results = result.get('results', {})
                    if rbl_results:
                        txtfile.write("\nRBL Check Results:\n")
                        for service, rbl_data in rbl_results.items():
                            status = rbl_data.get('status', 'UNKNOWN')
                            details = rbl_data.get('details', 'No details')
                            txtfile.write(f"  {service}: {status}")
                            if details and details != 'No details':
                                txtfile.write(f" - {details}")
                            txtfile.write("\n")
                    else:
                        txtfile.write("\nNo RBL check results available.\n")
                    
                    txtfile.write("\n" + "=" * 50 + "\n\n")
            
            self.logger.info(f"成功匯出 {len(results)} 筆結果到 TXT: {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"匯出 TXT 失敗: {str(e)}")
            return False
    
    def load_domains_from_file(self, filename: str) -> List[str]:
        """從檔案載入域名列表"""
        try:
            domains = []
            
            with open(filename, 'r', encoding='utf-8') as f:
                if filename.lower().endswith('.csv'):
                    # CSV 檔案處理
                    reader = csv.reader(f)
                    for row in reader:
                        if row:  # 跳過空行
                            domains.extend([cell.strip() for cell in row if cell.strip()])
                else:
                    # 純文字檔案處理
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):  # 跳過空行和註解
                            domains.append(line)
            
            # 去除重複並過濾空值
            unique_domains = list(set(domain for domain in domains if domain))
            
            self.logger.info(f"從檔案 {filename} 載入了 {len(unique_domains)} 個唯一域名")
            return unique_domains
            
        except Exception as e:
            self.logger.error(f"載入域名檔案失敗: {str(e)}")
            return []
    
    def create_backup(self, filename: str) -> Optional[str]:
        """建立檔案備份"""
        try:
            if not os.path.exists(filename):
                return None
            
            # 產生備份檔案名稱
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"{filename}.backup_{timestamp}"
            
            # 複製檔案
            import shutil
            shutil.copy2(filename, backup_filename)
            
            self.logger.info(f"已建立備份檔案: {backup_filename}")
            return backup_filename
            
        except Exception as e:
            self.logger.error(f"建立備份失敗: {str(e)}")
            return None
    
    def cleanup_old_backups(self, base_filename: str, keep_count: int = 5) -> int:
        """清理舊的備份檔案"""
        try:
            directory = os.path.dirname(base_filename) or '.'
            base_name = os.path.basename(base_filename)
            
            # 找到所有備份檔案
            backup_files = []
            for filename in os.listdir(directory):
                if filename.startswith(f"{base_name}.backup_"):
                    full_path = os.path.join(directory, filename)
                    backup_files.append((full_path, os.path.getmtime(full_path)))
            
            # 按修改時間排序（最新的在前）
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # 刪除多餘的備份檔案
            deleted_count = 0
            for backup_file, _ in backup_files[keep_count:]:
                try:
                    os.remove(backup_file)
                    deleted_count += 1
                    self.logger.info(f"已刪除舊備份檔案: {backup_file}")
                except Exception as e:
                    self.logger.error(f"刪除備份檔案失敗 {backup_file}: {str(e)}")
            
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"清理備份檔案失敗: {str(e)}")
            return 0
