#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RBL Email Domains Status Checker - 演示版本
不需要網路連線的演示版本

Author: Your Name
Date: 2024-01-01
Version: 1.0.0 (Demo)
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
import logging
import time
import random
from typing import Dict, List

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class DemoRBLChecker:
    """演示版 RBL 檢查器"""
    
    def __init__(self):
        self.rbl_services = [
            "Spamhaus SBL",
            "Spamhaus CSS", 
            "Spamhaus XBL",
            "Spamhaus PBL",
            "Barracuda",
            "SURBL",
            "URIBL",
            "SpamCop",
            "Invaluement",
            "PSBL"
        ]
    
    def check_single_domain(self, domain: str) -> Dict:
        """演示版域名檢查"""
        # 模擬檢查延遲
        time.sleep(1)
        
        # 模擬檢查結果
        results = {}
        for service in self.rbl_services:
            # 隨機產生結果，但讓知名域名通常是乾淨的
            if domain.lower() in ['google.com', 'microsoft.com', 'apple.com', 'amazon.com']:
                status = 'CLEAN'
                details = 'Domain is not listed'
            else:
                # 30% 機率被列入黑名單
                status = 'LISTED' if random.random() < 0.3 else 'CLEAN'
                details = 'Domain is listed in blacklist' if status == 'LISTED' else 'Domain is not listed'
            
            results[service] = {
                'status': status,
                'details': details,
                'raw_status': status.lower()
            }
        
        return {
            'domain': domain,
            'status': 'success',
            'message': '檢查完成 (演示模式)',
            'results': results,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }

class DemoMainWindow:
    """演示版主視窗"""
    
    def __init__(self, root: tk.Tk):
        self.root = root
        self.rbl_checker = DemoRBLChecker()
        self.current_results = []
        
        self.setup_main_window()
        self.create_widgets()
        self.setup_layout()
    
    def setup_main_window(self):
        """設定主視窗"""
        self.root.title("RBL Email Domains Status Checker v1.0 (演示版)")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # 設定樣式
        style = ttk.Style()
        try:
            style.theme_use('clam')
        except:
            pass
    
    def create_widgets(self):
        """建立 UI 元件"""
        # 主框架
        self.main_frame = ttk.Frame(self.root, padding="10")
        
        # 標題
        self.title_label = ttk.Label(
            self.main_frame,
            text="RBL Email Domains Status Checker (演示版)",
            font=("Arial", 16, "bold")
        )
        
        # 演示說明
        self.demo_label = ttk.Label(
            self.main_frame,
            text="這是演示版本，不會進行實際的網路查詢，結果為模擬資料",
            font=("Arial", 10),
            foreground="red"
        )
        
        # 輸入區域
        self.input_frame = ttk.LabelFrame(self.main_frame, text="域名輸入", padding="10")
        
        self.domain_label = ttk.Label(self.input_frame, text="請輸入要檢查的域名:")
        self.domain_var = tk.StringVar()
        self.domain_entry = ttk.Entry(
            self.input_frame,
            textvariable=self.domain_var,
            font=("Arial", 12),
            width=40
        )
        
        # 按鈕
        self.button_frame = ttk.Frame(self.input_frame)
        self.check_button = ttk.Button(
            self.button_frame,
            text="檢查 RBL 狀態 (演示)",
            command=self.check_domain
        )
        self.clear_button = ttk.Button(
            self.button_frame,
            text="清除",
            command=self.clear_input
        )
        
        # 進度條
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.main_frame,
            variable=self.progress_var,
            maximum=100,
            mode='determinate'
        )
        
        # 狀態標籤
        self.status_var = tk.StringVar(value="就緒 (演示模式)")
        self.status_label = ttk.Label(self.main_frame, textvariable=self.status_var)
        
        # 結果區域
        self.result_frame = ttk.LabelFrame(self.main_frame, text="檢查結果", padding="10")
        
        # 結果文字區域
        self.result_text = tk.Text(
            self.result_frame,
            height=15,
            width=80,
            font=("Consolas", 10),
            wrap=tk.WORD
        )
        
        # 滾動條
        self.scrollbar = ttk.Scrollbar(
            self.result_frame,
            orient="vertical",
            command=self.result_text.yview
        )
        self.result_text.configure(yscrollcommand=self.scrollbar.set)
        
        # 結果按鈕
        self.result_button_frame = ttk.Frame(self.result_frame)
        self.clear_results_button = ttk.Button(
            self.result_button_frame,
            text="清除結果",
            command=self.clear_results
        )
        
        # 綁定事件
        self.domain_entry.bind("<Return>", lambda e: self.check_domain())
    
    def setup_layout(self):
        """設定佈局"""
        # 主框架
        self.main_frame.grid(row=0, column=0, sticky="nsew")
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(5, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)
        
        # 標題
        self.title_label.grid(row=0, column=0, pady=(0, 10))
        self.demo_label.grid(row=1, column=0, pady=(0, 20))
        
        # 輸入區域
        self.input_frame.grid(row=2, column=0, sticky="ew", pady=(0, 10))
        self.input_frame.grid_columnconfigure(0, weight=1)
        
        self.domain_label.grid(row=0, column=0, sticky="w", pady=(0, 5))
        self.domain_entry.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        
        self.button_frame.grid(row=2, column=0, sticky="ew")
        self.check_button.grid(row=0, column=0, padx=(0, 5))
        self.clear_button.grid(row=0, column=1)
        
        # 進度條和狀態
        self.progress_bar.grid(row=3, column=0, sticky="ew", pady=(10, 5))
        self.status_label.grid(row=4, column=0, sticky="w")
        
        # 結果區域
        self.result_frame.grid(row=5, column=0, sticky="nsew", pady=(10, 0))
        self.result_frame.grid_rowconfigure(0, weight=1)
        self.result_frame.grid_columnconfigure(0, weight=1)
        
        self.result_text.grid(row=0, column=0, sticky="nsew")
        self.scrollbar.grid(row=0, column=1, sticky="ns")
        
        self.result_button_frame.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(10, 0))
        self.clear_results_button.grid(row=0, column=0)
    
    def check_domain(self):
        """檢查域名"""
        domain = self.domain_var.get().strip()
        
        if not domain:
            messagebox.showwarning("警告", "請輸入要檢查的域名")
            return
        
        # 簡單的域名格式檢查
        if '.' not in domain or len(domain) < 3:
            messagebox.showerror("錯誤", "請輸入有效的域名格式")
            return
        
        # 開始檢查
        self.status_var.set(f"正在檢查域名: {domain} (演示模式)")
        self.progress_var.set(0)
        self.check_button.config(state="disabled")
        
        # 模擬進度
        for i in range(0, 101, 10):
            self.progress_var.set(i)
            self.root.update()
            time.sleep(0.1)
        
        # 執行檢查
        result = self.rbl_checker.check_single_domain(domain)
        
        # 顯示結果
        self.display_result(result)
        
        # 重設狀態
        self.status_var.set("檢查完成 (演示模式)")
        self.progress_var.set(0)
        self.check_button.config(state="normal")
    
    def display_result(self, result: Dict):
        """顯示檢查結果"""
        self.current_results.append(result)
        
        # 格式化結果文字
        output = []
        output.append("=" * 60)
        output.append(f"域名: {result['domain']}")
        output.append(f"檢查時間: {result['timestamp']}")
        output.append(f"狀態: {result['message']}")
        output.append("")
        output.append("RBL 檢查結果:")
        output.append("-" * 40)
        
        listed_count = 0
        clean_count = 0
        
        for service, data in result['results'].items():
            status = data['status']
            details = data['details']
            
            if status == 'LISTED':
                status_icon = "❌"
                listed_count += 1
            else:
                status_icon = "✅"
                clean_count += 1
            
            output.append(f"{status_icon} {service}: {status}")
            if details:
                output.append(f"   詳細: {details}")
        
        output.append("")
        output.append(f"統計: 總計 {len(result['results'])} 個 RBL 服務")
        output.append(f"      黑名單: {listed_count} 個")
        output.append(f"      正常: {clean_count} 個")
        output.append("=" * 60)
        output.append("")
        
        # 插入到文字區域
        self.result_text.insert(tk.END, "\n".join(output))
        self.result_text.see(tk.END)
    
    def clear_input(self):
        """清除輸入"""
        self.domain_var.set("")
        self.domain_entry.focus()
    
    def clear_results(self):
        """清除結果"""
        if messagebox.askyesno("確認", "確定要清除所有結果嗎？"):
            self.result_text.delete(1.0, tk.END)
            self.current_results.clear()
            self.status_var.set("結果已清除 (演示模式)")

def main():
    """主函數"""
    try:
        # 建立主視窗
        root = tk.Tk()
        app = DemoMainWindow(root)
        
        # 顯示歡迎訊息
        messagebox.showinfo(
            "歡迎使用",
            "歡迎使用 RBL Email Domains Status Checker 演示版！\n\n"
            "這是一個演示版本，不會進行實際的網路查詢。\n"
            "所有檢查結果都是模擬資料，僅供展示功能使用。\n\n"
            "請輸入域名並點擊檢查按鈕來體驗功能。"
        )
        
        # 啟動主迴圈
        root.mainloop()
        
    except Exception as e:
        messagebox.showerror("錯誤", f"應用程式啟動失敗: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
