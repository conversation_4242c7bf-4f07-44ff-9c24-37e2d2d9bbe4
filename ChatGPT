我需要開發使用Pyhton寫出的MxToolbox Blacklist, 針對RBL域名和IP, 可以批次導入和輸入並透過API進行查詢產生結果, 最後將查找的結果匯出, 請先根據需求找出類似MxToolbox RBL功能 Blacklist, 並再將代碼寫出來至ui.py, 並產出main.py可以執行
Python 說：
已思考 2m 5s
Mx Toolbox風格rbl批次查詢工具：ui.py 與 Main

下面是兩個檔案的完整原始碼（請直接將兩段分別存成 ui.py 與 main.py 即可使用）。

ui.py
# ui.py
            fieldnames=list(asdict(results[0]).keys())
        )
        writer.writeheader()
        for r in results:
            writer.writerow(asdict(r))
    print(f"[輸出] CSV 已寫入 {out_path}")




def export_results_json(results: List[RBLResult], out_path: str) -> None:
    os.makedirs(os.path.dirname(out_path) or ".", exist_ok=True)
    with open(out_path, "w", encoding="utf-8") as f:
        json.dump([asdict(r) for r in results], f, ensure_ascii=False, indent=2)
    print(f"[輸出] JSON 已寫入 {out_path}")




# -------------------------
# CLI 參數（由 main.py 呼叫亦可重用）
# -------------------------


def build_arg_parser() -> argparse.ArgumentParser:
    p = argparse.ArgumentParser(
        description=(
            "RBL 黑名單批次查詢（支援 HetrixTools、RBLTracker；以 API 查詢域名與 IPv4），" \
            "可讀取清單、手動輸入並輸出 CSV/JSON。"
        )
    )
    p.add_argument("--provider", choices=["hetrixtools", "rbltracker"], default="hetrixtools",
                   help="選擇 API 提供者（預設 hetrixtools）")
    p.add_argument("--in", dest="in_files", nargs="*", default=[],
                   help="匯入檔案路徑（支援 .txt/.csv/.tsv，取第一欄）")
    p.add_argument("--targets", dest="targets_inline", default=None,
                   help="以逗號或空白分隔的一次性清單（domain 或 IPv4）")
    p.add_argument("--out", dest="out_path", default="results.csv",
                   help="輸出檔案路徑（副檔名 .csv 或 .json）")
    p.add_argument("--concurrency", type=int, default=4,
                   help="並行查詢數（避免太大以免被限速）")
    p.add_argument("--timeout", type=int, default=300,
                   help="單一項目最多等待秒數（含輪詢），預設 300 秒")
    p.add_argument("--print", dest="do_print", action="store_true",
                   help="查詢完成後在終端機列印摘要表格")
    # API 認證：用環境變數最方便
    # HetrixTools: HETRIX_API_TOKEN
    # RBLTracker: RBLTRACKER_SID / RBLTRACKER_TOKEN
    return p




def resolve_output_exporter(path: str):
    ext = os.path.splitext(path)[1].lower()
    if ext == ".json":
        return export_results_json
    return export_results_csv
main.py
        self.sid = sid
        if key not in seen:
            seen.add(key)
            uniq.append(key)


    if not uniq:
        print("[錯誤] 沒有可查詢的目標。請用 --in 或 --targets 輸入。", file=sys.stderr)
        sys.exit(2)


    # 建立 provider client
    provider = args.provider
    max_wait = int(args.timeout)
    if provider == "hetrixtools":
        token = os.getenv("HETRIX_API_TOKEN", "").strip()
        client = HetrixToolsClient(token)
    elif provider == "rbltracker":
        sid = os.getenv("RBLTRACKER_SID", "").strip()
        tok = os.getenv("RBLTRACKER_TOKEN", "").strip()
        client = RBLTrackerClient(sid, tok)
    else:
        print(f"[錯誤] 不支援的 provider: {provider}", file=sys.stderr)
        sys.exit(2)


    # 併發處理
    results: List[RBLResult] = []
    total = len(uniq)
    done = 0
    print(f"[資訊] Provider={provider}, 總計 {total} 項，並行={args.concurrency}，timeout={max_wait}s …")
    with cf.ThreadPoolExecutor(max_workers=args.concurrency) as ex:
        futs = [
            ex.submit(query_one, provider, client, target, kind, max_wait)
            for (target, kind) in uniq
        ]
        for fut in cf.as_completed(futs):
            res = fut.result()
            results.append(res)
            done += 1
            if done % 1 == 0:
                print(f"  進度：{done}/{total} 完成", end="\r")
    print("\n[完成] 全部查詢已完成。")


    # 輸出
    exporter = resolve_output_exporter(args.out_path)
    exporter(results, args.out_path)


    if args.do_print:
        print_summary(results)




if __name__ == "__main__":
    main()
使用方式（摘要）

安裝相依：

python -m pip install --upgrade pip
pip install requests

設定 API 金鑰（擇一提供者）：

HetrixTools：

# 於 shell 設定（範例）
export HETRIX_API_TOKEN="你的_Hetrix_API_TOKEN"

RBLTracker：

export RBLTRACKER_SID="你的_RBLTracker_SID"
export RBLTRACKER_TOKEN="你的_RBLTracker_TOKEN"

準備清單（可 .txt/.csv/.tsv；第一欄為 domain 或 IPv4）：

example.com
*******

執行範例：

# 以 HetrixTools 查詢，輸入檔 + 另加臨時目標，輸出 CSV
python main.py --provider hetrixtools --in targets.txt --targets "gmail.com *******" --out results.csv --print


# 以 RBLTracker 查詢，輸出 JSON
python main.py --provider rbltracker --in list.csv --out out.json --print

小提醒：HetrixTools 的 API 查詢屬於即時黑名單檢測，官方文件說明結果可能需輪詢數十秒至數分鐘才會完成；程式已內建指數回退輪詢（預設最多等待 300 秒，可改 --timeout）。

已思考 16s

好的！我先幫你把「類似 MxToolbox 的 RBL/Blacklist 查詢解法」盤點過，接著已把可直接執行的程式拆成兩個檔案：ui.py（介面/IO）與 main.py（核心查詢與匯出），都已放在右側的檔案區（canvas）了。你只要把兩段各自存成對應檔名即可執行。

先幫你挑選「類似 MxToolbox Blacklist」且有 API 的方案

MxToolbox REST API（付費/需登入查看文件）：可做 SuperTool 的查詢（包含 Blacklist、DNS、Email、Network 等），API 文件需登入帳戶於 Settings → API 檢視。適合已經是 MxToolbox 企業/專業用戶直接整合。 
mxtoolbox.com
+2
mxtoolbox.com
+2
exchange.xforce.ibmcloud.com

HetrixTools Blacklist Check API（有額度/點數制，公開文件）：提供「即時黑名單檢測」API，支援 IPv4 與 Domain/Hostname，查詢會進入排程、最多可能需等待數十秒～數分鐘完成，完成後回傳 blacklisted_count、blacklisted_on（含各 RBL 與 delist 連結）與報告連結。程式已預設支援並內建輪詢。 
HetrixTools
+2
HetrixTools
+2

RBLTracker API（付費/公開文件）：以 POST /3.0/check/start 觸發檢查，再用 GET /3.0/check/status/{id} 輪詢狀態，可同時檢查 IP 或網域；身份驗證採 HTTP Basic（SID + Token）。我也把這家整合進程式，讓你可自由切換供應商。 
RBLTracker

其他像 MultiRBL/Spamhaus 也有查詢工具；Spamhaus提供付費 Intelligence API，免費 DNSBL 主要走 DNS 查詢，不是 HTTP API，與你需求的「透過 API 批次查詢/匯出」略有出入。 
Spamhaus
The Spamhaus Project

我已經幫你把可執行的程式生好（ui.py / main.py）

右側已附上兩個檔案完整原始碼（請直接存成 ui.py、main.py）：

支援供應商：hetrixtools（預設）、rbltracker。

支援輸入：批次匯入 .txt/.csv/.tsv（取第一欄）、或 --targets 直接輸入（以逗號/空白分隔）。

輸出：CSV（預設）或 JSON，欄位含 target/kind/provider/listed/listed_count/listed_on/delist_links/report_link/...。

並行處理：--concurrency 控制；輪詢：有指數回退（--timeout 預設 300 秒）。

相依：Python 3.9+、第三方只用 requests。

快速執行
python -m pip install --upgrade pip
pip install requests


供應商金鑰（擇一設定環境變數）：

# HetrixTools
export HETRIX_API_TOKEN="你的_Hetrix_API_TOKEN"

# RBLTracker
export RBLTRACKER_SID="你的_RBLTracker_SID"
export RBLTRACKER_TOKEN="你的_RBLTRACKER_TOKEN"


範例：

# 以 HetrixTools 查  targets.txt + 臨時輸入，輸出 CSV 並列印摘要
python main.py --provider hetrixtools --in targets.txt --targets "gmail.com *******" --out results.csv --print

# 以 RBLTracker 查詢，輸出 JSON
python main.py --provider rbltracker --in list.csv --out out.json --print


備註：HetrixTools 的即時查詢屬「啟動→排程→完成」模式（官方文件指出可能要等到幾十秒或數分鐘才生成結果），程式會自動輪詢直到完成或超時。你可以用 --timeout 調整等待上限。 
HetrixTools

如果你最後要指定用 MxToolbox 自家的 API（而非 HetrixTools/RBLTracker），也沒問題：MxToolbox 的 API 文件在你登入帳戶後的 Settings → API，提供 Blacklist 等 lookup 方法；只要你把授權方式/端點給我（或提供能存取文件的要點），我可以把第三個 provider（mxtoolbox）加到同一套介面內，維持批次匯入/輪詢/匯出的一致使用體驗。 
mxtoolbox.com

要不要我再加上一份 requirements.txt 與範例 targets.txt？或需要把輸出改成 Excel（.xlsx）與更豐富的報表欄位，也都可以直接幫你加上。