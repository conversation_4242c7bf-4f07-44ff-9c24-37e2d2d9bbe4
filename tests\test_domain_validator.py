#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
域名驗證器測試模組
"""

import unittest
import sys
import os

# 添加專案根目錄到路徑
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.domain_validator import DomainValidator

class TestDomainValidator(unittest.TestCase):
    """域名驗證器測試類別"""
    
    def setUp(self):
        """測試前設定"""
        self.validator = DomainValidator()
    
    def test_is_valid_domain_valid(self):
        """測試有效域名"""
        valid_domains = [
            'google.com',
            'example.org',
            'test.co.uk',
            'sub.domain.com',
            'a.b.c.d.com',
            'xn--fsq.com',  # 國際化域名
            '123.com',
            'test-domain.com',
            'a.com'
        ]
        
        for domain in valid_domains:
            with self.subTest(domain=domain):
                self.assertTrue(
                    self.validator.is_valid_domain(domain),
                    f"域名 {domain} 應該是有效的"
                )
    
    def test_is_valid_domain_invalid(self):
        """測試無效域名"""
        invalid_domains = [
            '',
            None,
            'invalid',
            '.com',
            'domain.',
            'domain..com',
            'domain .com',
            'domain@com',
            '-domain.com',
            'domain-.com',
            'very-long-domain-name-that-exceeds-the-maximum-length-limit-for-domain-names-and-should-be-rejected.com',
            'a' * 64 + '.com',  # 標籤太長
            'domain.c',  # TLD 太短
        ]
        
        for domain in invalid_domains:
            with self.subTest(domain=domain):
                self.assertFalse(
                    self.validator.is_valid_domain(domain),
                    f"域名 {domain} 應該是無效的"
                )
    
    def test_is_valid_email_valid(self):
        """測試有效電子郵件"""
        valid_emails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
        
        for email in valid_emails:
            with self.subTest(email=email):
                self.assertTrue(
                    self.validator.is_valid_email(email),
                    f"電子郵件 {email} 應該是有效的"
                )
    
    def test_is_valid_email_invalid(self):
        """測試無效電子郵件"""
        invalid_emails = [
            '',
            None,
            'invalid',
            '@domain.com',
            'user@',
            'user@domain',
            'user.domain.com',
            '<EMAIL>',
            'user @domain.com',
            'user@domain .com'
        ]
        
        for email in invalid_emails:
            with self.subTest(email=email):
                self.assertFalse(
                    self.validator.is_valid_email(email),
                    f"電子郵件 {email} 應該是無效的"
                )
    
    def test_extract_domain_from_email(self):
        """測試從電子郵件提取域名"""
        test_cases = [
            ('<EMAIL>', 'example.com'),
            ('<EMAIL>', 'domain.org'),
            ('<EMAIL>', 'example.co.uk'),
            ('invalid-email', None),
            ('', None),
            (None, None)
        ]
        
        for email, expected_domain in test_cases:
            with self.subTest(email=email):
                result = self.validator.extract_domain_from_email(email)
                self.assertEqual(result, expected_domain)
    
    def test_extract_domain_from_url(self):
        """測試從 URL 提取域名"""
        test_cases = [
            ('https://www.example.com/path', 'www.example.com'),
            ('http://domain.org', 'domain.org'),
            ('example.com', 'example.com'),
            ('www.test.co.uk:8080', 'www.test.co.uk'),
            ('https://sub.domain.com/path?query=1', 'sub.domain.com'),
            ('invalid-url', None),
            ('', None)
        ]
        
        for url, expected_domain in test_cases:
            with self.subTest(url=url):
                result = self.validator.extract_domain_from_url(url)
                self.assertEqual(result, expected_domain)
    
    def test_normalize_domain(self):
        """測試域名標準化"""
        test_cases = [
            ('Example.COM', 'example.com'),
            ('  domain.org  ', 'domain.org'),
            ('www.example.com', 'example.com'),
            ('https://www.domain.org', 'domain.org'),
            ('http://example.com/', 'example.com'),
            ('domain.com.', 'domain.com'),
            ('', ''),
            ('WWW.TEST.CO.UK', 'test.co.uk')
        ]
        
        for input_domain, expected in test_cases:
            with self.subTest(input_domain=input_domain):
                result = self.validator.normalize_domain(input_domain)
                self.assertEqual(result, expected)
    
    def test_validate_domain_list(self):
        """測試域名列表驗證"""
        domains = [
            'google.com',
            'invalid',
            'example.org',
            '',
            'test.co.uk',
            'domain..com',
            'valid-domain.com'
        ]
        
        valid, invalid = self.validator.validate_domain_list(domains)
        
        # 檢查有效域名
        expected_valid = ['google.com', 'example.org', 'test.co.uk', 'valid-domain.com']
        self.assertEqual(sorted(valid), sorted(expected_valid))
        
        # 檢查無效域名
        expected_invalid = ['invalid', '', 'domain..com']
        self.assertEqual(sorted(invalid), sorted(expected_invalid))
    
    def test_suggest_corrections(self):
        """測試域名修正建議"""
        test_cases = [
            ('gmial.com', ['gmail.com']),
            ('yahooo.com', ['yahoo.com']),
            ('hotmial.com', ['hotmail.com']),
            ('outlok.com', ['outlook.com']),
            ('testdomain', ['testdomain.com', 'testdomain.org', 'testdomain.net']),
            ('', []),
            ('valid.domain.com', [])  # 已經是有效域名，不需要建議
        ]
        
        for domain, expected_suggestions in test_cases:
            with self.subTest(domain=domain):
                suggestions = self.validator.suggest_corrections(domain)
                
                if expected_suggestions:
                    # 檢查是否包含預期的建議
                    for suggestion in expected_suggestions:
                        if suggestion in suggestions:
                            break
                    else:
                        self.fail(f"域名 {domain} 的建議中應該包含 {expected_suggestions} 中的至少一個")
                else:
                    # 對於有效域名或空域名，建議應該是空的或很少
                    self.assertLessEqual(len(suggestions), 1)
    
    @unittest.skip("需要網路連線的測試")
    def test_check_domain_dns(self):
        """測試 DNS 檢查（需要網路連線）"""
        # 這個測試需要實際的網路連線
        self.assertTrue(self.validator.check_domain_dns('google.com'))
        self.assertFalse(self.validator.check_domain_dns('non-existent-domain-12345.com'))
    
    @unittest.skip("需要 dnspython 套件")
    def test_get_mx_records(self):
        """測試 MX 記錄查詢（需要 dnspython 套件）"""
        # 這個測試需要安裝 dnspython 套件
        mx_records = self.validator.get_mx_records('google.com')
        self.assertIsInstance(mx_records, list)

if __name__ == '__main__':
    # 設定測試套件
    unittest.main(verbosity=2)
