#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI 測試腳本 - 測試 RBL 查詢工具的 GUI 功能
"""

import sys
import os

# 確保可以導入 ui 模組
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """測試模組導入"""
    try:
        from ui import RBLCheckerGUI, RBLResult
        print("✅ 模組導入成功")
        return True
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False

def test_gui_creation():
    """測試 GUI 建立"""
    try:
        from ui import RBLCheckerGUI
        
        # 建立 GUI 實例但不顯示
        app = RBLCheckerGUI()
        
        # 測試基本屬性
        assert hasattr(app, 'root'), "缺少 root 屬性"
        assert hasattr(app, 'hetrix_token_var'), "缺少 hetrix_token_var 屬性"
        assert hasattr(app, 'rbltracker_sid_var'), "缺少 rbltracker_sid_var 屬性"
        assert hasattr(app, 'rbltracker_token_var'), "缺少 rbltracker_token_var 屬性"
        
        # 測試方法
        assert hasattr(app, 'show_api_settings'), "缺少 show_api_settings 方法"
        assert hasattr(app, 'update_api_status'), "缺少 update_api_status 方法"
        
        print("✅ GUI 建立測試成功")
        
        # 清理
        app.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI 建立測試失敗: {e}")
        return False

def test_api_status():
    """測試 API 狀態功能"""
    try:
        from ui import RBLCheckerGUI
        
        app = RBLCheckerGUI()
        
        # 測試初始狀態
        app.update_api_status()
        status = app.api_status_var.get()
        assert "未設定" in status, f"初始狀態不正確: {status}"
        
        # 測試設定 HetrixTools
        app.hetrix_token_var.set("test_token_123456789")
        app.provider_var.set("hetrixtools")
        app.update_api_status()
        status = app.api_status_var.get()
        assert "✅" in status and "HetrixTools" in status, f"HetrixTools 狀態不正確: {status}"
        
        # 測試設定 RBLTracker
        app.rbltracker_sid_var.set("test_sid_123")
        app.rbltracker_token_var.set("test_token_456")
        app.provider_var.set("rbltracker")
        app.update_api_status()
        status = app.api_status_var.get()
        assert "✅" in status and "RBLTracker" in status, f"RBLTracker 狀態不正確: {status}"
        
        print("✅ API 狀態測試成功")
        
        # 清理
        app.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ API 狀態測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("開始測試 RBL 查詢工具 GUI...")
    print("=" * 50)
    
    tests = [
        ("模組導入", test_imports),
        ("GUI 建立", test_gui_creation),
        ("API 狀態", test_api_status),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n測試: {test_name}")
        print("-" * 30)
        if test_func():
            passed += 1
        else:
            print(f"測試失敗: {test_name}")
    
    print("\n" + "=" * 50)
    print(f"測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！GUI 功能正常。")
        print("\n可以使用以下命令啟動 GUI:")
        print("python gui_launcher.py")
        return True
    else:
        print("❌ 部分測試失敗，請檢查錯誤訊息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
