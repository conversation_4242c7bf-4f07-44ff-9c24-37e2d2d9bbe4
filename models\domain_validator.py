#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
域名驗證模組
提供域名格式驗證和相關工具函數
"""

import re
import socket
import logging
from typing import List, Tuple, Optional
from urllib.parse import urlparse

class DomainValidator:
    """域名驗證器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 域名格式正則表達式 - 更嚴格的驗證
        self.domain_pattern = re.compile(
            r'^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$'
        )
        
        # 電子郵件格式正則表達式 - 更嚴格的驗證
        self.email_pattern = re.compile(
            r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}$'
        )
        
        # 常見的頂級域名
        self.common_tlds = {
            'com', 'org', 'net', 'edu', 'gov', 'mil', 'int',
            'tw', 'cn', 'jp', 'kr', 'uk', 'de', 'fr', 'it',
            'es', 'ru', 'br', 'au', 'ca', 'in', 'mx'
        }
    
    def is_valid_domain(self, domain: str) -> bool:
        """檢查域名格式是否有效"""
        if not domain or not isinstance(domain, str):
            return False
        
        domain = domain.strip().lower()
        
        # 基本長度檢查
        if len(domain) < 1 or len(domain) > 253:
            return False
        
        # 正則表達式檢查
        if not self.domain_pattern.match(domain):
            return False
        
        # 檢查各個標籤的長度
        labels = domain.split('.')
        for label in labels:
            if len(label) < 1 or len(label) > 63:
                return False
        
        return True
    
    def is_valid_email(self, email: str) -> bool:
        """檢查電子郵件格式是否有效"""
        if not email or not isinstance(email, str):
            return False
        
        email = email.strip().lower()
        return bool(self.email_pattern.match(email))
    
    def extract_domain_from_email(self, email: str) -> Optional[str]:
        """從電子郵件地址中提取域名"""
        if not self.is_valid_email(email):
            return None
        
        return email.split('@')[1]
    
    def extract_domain_from_url(self, url: str) -> Optional[str]:
        """從 URL 中提取域名"""
        try:
            # 如果不是有效的 URL 格式，直接返回 None
            if not url or not isinstance(url, str):
                return None

            # 檢查是否包含無效字符
            if any(char in url for char in [' ', '\t', '\n', '\r']):
                return None

            if not url.startswith(('http://', 'https://')):
                # 檢查是否看起來像域名
                if '.' not in url or len(url.split('.')) < 2:
                    return None
                url = 'http://' + url

            parsed = urlparse(url)
            domain = parsed.netloc.lower()

            # 移除端口號
            if ':' in domain:
                domain = domain.split(':')[0]

            return domain if domain and self.is_valid_domain(domain) else None

        except Exception as e:
            self.logger.error(f"解析 URL 時發生錯誤: {str(e)}")
            return None
    
    def normalize_domain(self, domain: str) -> str:
        """標準化域名格式"""
        if not domain:
            return ""
        
        domain = domain.strip().lower()
        
        # 移除協議前綴
        if domain.startswith(('http://', 'https://')):
            domain = self.extract_domain_from_url(domain) or domain
        
        # 移除 www. 前綴
        if domain.startswith('www.'):
            domain = domain[4:]
        
        # 移除尾隨的點
        domain = domain.rstrip('.')
        
        return domain
    
    def validate_domain_list(self, domains: List[str]) -> Tuple[List[str], List[str]]:
        """驗證域名列表，返回有效和無效的域名"""
        valid_domains = []
        invalid_domains = []
        
        for domain in domains:
            normalized = self.normalize_domain(domain)
            if self.is_valid_domain(normalized):
                valid_domains.append(normalized)
            else:
                invalid_domains.append(domain)
        
        return valid_domains, invalid_domains
    
    def check_domain_dns(self, domain: str) -> bool:
        """檢查域名是否有有效的 DNS 記錄"""
        try:
            socket.gethostbyname(domain)
            return True
        except socket.gaierror:
            return False
    
    def get_mx_records(self, domain: str) -> List[str]:
        """取得域名的 MX 記錄"""
        try:
            import dns.resolver
            mx_records = []
            answers = dns.resolver.resolve(domain, 'MX')
            for answer in answers:
                mx_records.append(str(answer.exchange))
            return mx_records
        except ImportError:
            self.logger.warning("dns.resolver 模組未安裝，無法查詢 MX 記錄")
            return []
        except Exception as e:
            self.logger.error(f"查詢 MX 記錄時發生錯誤: {str(e)}")
            return []
    
    def suggest_corrections(self, domain: str) -> List[str]:
        """為無效域名提供修正建議"""
        suggestions = []
        
        if not domain:
            return suggestions
        
        domain = domain.strip().lower()
        
        # 常見錯誤修正
        corrections = {
            'gmial.com': 'gmail.com',
            'yahooo.com': 'yahoo.com',
            'hotmial.com': 'hotmail.com',
            'outlok.com': 'outlook.com'
        }
        
        if domain in corrections:
            suggestions.append(corrections[domain])
        
        # 如果缺少頂級域名，嘗試添加常見的
        if '.' not in domain:
            for tld in ['com', 'org', 'net']:
                suggestions.append(f"{domain}.{tld}")
        
        # 如果有多個點但格式不正確，嘗試修正
        if domain.count('.') > 1:
            parts = domain.split('.')
            if len(parts) >= 2:
                suggestions.append(f"{parts[-2]}.{parts[-1]}")
        
        return suggestions[:5]  # 最多返回 5 個建議
