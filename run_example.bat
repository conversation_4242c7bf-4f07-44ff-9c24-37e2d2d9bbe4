@echo off
REM run_example.bat - 範例批次檔，提供 HetrixTools 與 RBLTracker 查詢選項

echo ================================================
echo   RBL Blacklist 批次查詢工具 - 範例執行檔
echo ================================================
echo.
echo 請選擇要使用的 Provider:
echo   1) HetrixTools
echo   2) RBLTracker
echo.

set /p choice=輸入數字 (1 或 2): 

if "%choice%"=="1" goto HETRIX
if "%choice%"=="2" goto RBLTRACKER

echo [錯誤] 無效的選項，請輸入 1 或 2
goto END

:HETRIX
echo -------------------------------------------------------
echo [INFO] 使用 HetrixTools 查詢 targets.txt ...
echo 請先確認已經設定環境變數 HETRIX_API_TOKEN
echo -------------------------------------------------------
py main.py --provider hetrixtools --in targets.txt --out results_hetrix.csv --print
echo -------------------------------------------------------
echo 查詢完成，結果已輸出至 results_hetrix.csv
goto END

:RBLTRACKER
echo -------------------------------------------------------
echo [INFO] 使用 RBLTracker 查詢 targets.txt ...
echo 請先確認已經設定環境變數 RBLTRACKER_SID 和 RBLTRACKER_TOKEN
echo -------------------------------------------------------
py main.py --provider rbltracker --in targets.txt --out results_rbl.json --print
echo -------------------------------------------------------
echo 查詢完成，結果已輸出至 results_rbl.json
goto END

:END
pause
