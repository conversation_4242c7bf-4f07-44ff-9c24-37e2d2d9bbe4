# 🚀 RBL Email Domains Status Checker 快速啟動指南

## 📋 專案概述

這是一個用於檢查電子郵件域名在各種實時黑名單 (RBL - Real-time Blackhole List) 狀態的 Python 應用程式。

## ⚡ 快速啟動

### 方法 1: 主程式（推薦）
```bash
py main.py
```
- ✅ 完整功能介面
- ✅ 無需額外套件
- ✅ 立即可用
- ℹ️ 使用模擬資料演示

### 方法 2: 演示版本
```bash
py demo_main.py
```
- ✅ 簡化版介面
- ✅ 無需網路連線
- ✅ 快速體驗
- ⚠️ 功能較少

### 方法 3: 使用啟動腳本
```bash
# Windows
start.bat

# Linux/macOS
chmod +x start.sh
./start.sh
```

## 🎯 主要功能

### 1. 單一域名檢查
- 輸入域名（如：google.com）
- 點擊「檢查 RBL 狀態」
- 查看詳細結果

### 2. 批量檢查（完整版）
- 點擊「批量檢查」按鈕
- 輸入多個域名（每行一個）
- 同時檢查多個域名

### 3. 結果匯出（完整版）
- 支援 CSV、JSON、XML、TXT 格式
- 點擊「匯出結果」選擇格式

## 📦 打包成 exe 檔案

```bash
# 安裝打包工具
pip install pyinstaller

# 執行打包腳本
py build_exe.py

# 或手動打包
pyinstaller --onefile --windowed --name "RBL_Checker" main.py
```

打包後的檔案位於 `dist/` 目錄。

## 🧪 執行測試

```bash
# 執行所有測試
py run_tests.py

# 執行特定測試
py run_tests.py test_domain_validator
```

## 📁 專案結構

```
Python_RBL-email-domains/
├── 📄 main.py              # 完整版主程式
├── 📄 demo_main.py         # 演示版主程式
├── 📁 models/              # 業務邏輯
├── 📁 views/               # 使用者介面
├── 📁 utils/               # 工具模組
├── 📁 templates/           # UI 模板
├── 📁 tests/               # 測試檔案
├── 📄 requirements.txt     # 相依套件
├── 📄 build_exe.py         # 打包腳本
└── 📄 README.md            # 詳細說明
```

## 🔧 疑難排解

### 問題 1: 缺少套件
```bash
# 解決方案
pip install -r requirements.txt
```

### 問題 2: Python 版本問題
- 需要 Python 3.7 或更新版本
- 檢查版本：`py --version`

### 問題 3: 網路連線問題
- 使用演示版本：`py demo_main.py`
- 檢查防火牆設定

### 問題 4: GUI 無法顯示
- 確保已安裝 tkinter
- Windows: 通常已內建
- Linux: `sudo apt-get install python3-tk`

## 📝 使用範例

### 檢查知名域名
```
google.com
microsoft.com
apple.com
amazon.com
```

### 檢查可疑域名
```
suspicious-domain.com
spam-site.net
malware-host.org
```

## 🎨 功能展示

### 主介面
- 清晰的域名輸入框
- 即時檢查進度顯示
- 詳細的結果展示

### 檢查結果
- ✅ 正常域名：綠色勾號
- ❌ 黑名單域名：紅色叉號
- ❓ 未知狀態：問號標示

### 統計資訊
- 總檢查數量
- 黑名單數量
- 正常域名數量

## 🚀 進階使用

### 自訂設定
建立 `config.json` 檔案：
```json
{
  "rbl": {
    "timeout": 30,
    "max_workers": 5
  },
  "ui": {
    "theme": "clam"
  }
}
```

### 批量檢查檔案格式
```
# 範例域名檔案 (domains.txt)
google.com
microsoft.com
apple.com
# 註解行會被忽略
```

## 📞 技術支援

- 📖 詳細文件：README.md
- 🔧 開發文件：DEVELOPMENT.md
- 🐛 問題回報：GitHub Issues
- 💡 功能建議：GitHub Discussions

## 📄 授權條款

此專案採用 MIT 授權條款，詳見 LICENSE 檔案。

---

**立即開始使用：**
```bash
py demo_main.py
```

享受 RBL 檢查的便利！ 🎉
