# main.py

# 核心：呼叫不同提供者 API（HetrixTools / RBLTracker），並行處理、輪詢、輸出

# 需求：Python 3.9+，第三方套件：requests（pip install requests）

from __future__ import annotations
import concurrent.futures as cf
import os
import sys
import time
from datetime import datetime, timezone
from typing import Dict, Any, List, Tuple
import requests
import re

from ui import (
    RBLResult,
    build_arg_parser,
    classify_target,
    load_targets_from_files,
    parse_targets_inline,
    print_summary,
    resolve_output_exporter,
)

# -------------------------
# Provider: HetrixTools
# -------------------------

class HetrixToolsClient:
    BASE = "https://api.hetrixtools.com/v2"

    def __init__(self, token: str, timeout: int = 30):
        if not token:
            raise ValueError("缺少 HetrixTools API token（環境變數 HETRIX_API_TOKEN）")
        self.token = token
        self.timeout = timeout
        self.session = requests.Session()

    def _url(self, kind: str, target: str) -> str:
        if kind == "ipv4":
            return f"{self.BASE}/{self.token}/blacklist-check/ipv4/{target}/"
        elif kind == "domain":
            return f"{self.BASE}/{self.token}/blacklist-check/domain/{target}/"
        else:
            raise ValueError(f"不支援的種類: {kind}")

    def check_once(self, kind: str, target: str) -> Dict[str, Any]:
        url = self._url(kind, target)
        r = self.session.get(url, timeout=self.timeout)
        r.raise_for_status()
        return r.json()

    def wait_until_complete(self, kind: str, target: str, max_wait: int) -> Tuple[Dict[str, Any] | None, str | None]:
        start = time.time()
        sleep = 2.0
        last_err: str | None = None
        while True:
            try:
                data = self.check_once(kind, target)
            except requests.HTTPError as e:
                try:
                    data = e.response.json()
                except Exception:
                    return None, f"HTTP {e.response.status_code}"

            status = data.get("status")
            if status == "SUCCESS":
                return data, None
            err = data.get("error_message") or data.get("status_message") or str(data)
            last_err = err
            if "in progress" in (err or "").lower():
                if time.time() - start > max_wait:
                    return None, f"timeout after {max_wait}s (still in progress)"
                time.sleep(sleep)
                sleep = min(sleep * 1.5, 10)
                continue
            return None, err

# -------------------------

# Provider: RBLTracker

# -------------------------

class RBLTrackerClient:
    BASE = "https://api.rbltracker.com/3.0"

    def __init__(self, sid: str, token: str, timeout: int = 30):
        if not sid or not token:
            raise ValueError("缺少 RBLTracker SID/TOKEN（環境變數 RBLTRACKER_SID / RBLTRACKER_TOKEN）")
        self.sid = sid
        self.token = token
        self.timeout = timeout
        self.session = requests.Session()
        self.session.auth = (sid, token)

    def start_check(self, host: str, details: int = 1) -> str:
        url = f"{self.BASE}/check/start.json"
        r = self.session.post(url, data={"host": host, "details": details}, timeout=self.timeout)
        r.raise_for_status()
        data = r.json()
        if data.get("status_code") != 200:
            raise RuntimeError(data.get("status_message") or str(data))
        return data["data"]["id"]

    def get_status(self, check_id: str, details: int = 1) -> Dict[str, Any]:
        url = f"{self.BASE}/check/status/{check_id}.json"
        r = self.session.get(url, params={"details": details}, timeout=self.timeout)
        r.raise_for_status()
        return r.json()

    def wait_until_complete(self, host: str, max_wait: int) -> Tuple[Dict[str, Any] | None, str | None]:
        try:
            cid = self.start_check(host)
        except requests.HTTPError as e:
            try:
                return None, e.response.json().get("status_message")
            except Exception:
                return None, f"HTTP {e.response.status_code}"
        except Exception as e:
            return None, str(e)

        start = time.time()
        sleep = 2.0
        while True:
            try:
                data = self.get_status(cid, details=1)
            except requests.HTTPError as e:
                try:
                    data = e.response.json()
                except Exception:
                    return None, f"HTTP {e.response.status_code}"
            status_msg = data.get("status_message", "")
            payload = data.get("data") or {}
            if payload.get("status") == "completed":
                return data, None
            if time.time() - start > max_wait:
                return None, f"timeout after {max_wait}s (still processing)"
            time.sleep(sleep)
            sleep = min(sleep * 1.5, 10)

# -------------------------

# 統一結果格式

# -------------------------

def normalize_from_hetrixtools(target: str, kind: str, raw: Dict[str, Any]) -> RBLResult:
    listed_count = int(raw.get("blacklisted_count") or 0)
    listed = listed_count > 0
    listed_on = [x.get("rbl", "") for x in (raw.get("blacklisted_on") or []) if x.get("rbl")]
    delist_links = [x.get("delist", "") for x in (raw.get("blacklisted_on") or []) if x.get("delist")]
    report_link = (raw.get("links") or {}).get("report_link")
    return RBLResult(
        target=target,
        kind=kind,
        provider="hetrixtools",
        listed=listed,
        listed_count=listed_count,
        listed_on=listed_on,
        delist_links=delist_links,
        report_link=report_link,
        raw=raw,
        error=None,
        checked_at=datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
    )

def normalize_from_rbltracker(target: str, kind: str, raw: Dict[str, Any]) -> RBLResult:
    data = raw.get("data") or {}
    listed = bool(data.get("listed") or 0)
    listed_count = int(data.get("listed_count") or 0)
    details = data.get("listed_details") or []
    listed_on = []
    delist_links: List[str] = []
    for d in details:
        if d.get("listed"):
            listed_on.append(str(d.get("rbl")))
            info = str(d.get("details") or "")
            m = re.findall(r"https?://\S+", info)
            delist_links.extend(m)
    return RBLResult(
        target=target,
        kind=kind,
        provider="rbltracker",
        listed=listed,
        listed_count=listed_count,
        listed_on=listed_on,
        delist_links=delist_links,
        report_link=None,
        raw=raw,
        error=None,
        checked_at=datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
    )

# -------------------------

# 查詢封裝

# -------------------------

def query_one(provider: str, client, target: str, kind: str, max_wait: int) -> RBLResult:
    try:
        if provider == "hetrixtools":
            raw, err = client.wait_until_complete(kind, target, max_wait=max_wait)
            if err:
                return RBLResult(
                    target=target,
                    kind=kind,
                    provider=provider,
                    listed=False,
                    listed_count=0,
                    listed_on=[],
                    delist_links=[],
                    report_link=None,
                    raw={"error": err},
                    error=err,
                    checked_at=datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
                )
            return normalize_from_hetrixtools(target, kind, raw)
        elif provider == "rbltracker":
            raw, err = client.wait_until_complete(target, max_wait=max_wait)
            if err:
                return RBLResult(
                    target=target,
                    kind=kind,
                    provider=provider,
                    listed=False,
                    listed_count=0,
                    listed_on=[],
                    delist_links=[],
                    report_link=None,
                    raw={"error": err},
                    error=err,
                    checked_at=datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
                )
            return normalize_from_rbltracker(target, kind, raw)
        else:
            raise ValueError(f"未知 provider: {provider}")
    except Exception as e:
        return RBLResult(
            target=target,
            kind=kind,
            provider=provider,
            listed=False,
            listed_count=0,
            listed_on=[],
            delist_links=[],
            report_link=None,
            raw={},
            error=str(e),
            checked_at=datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
        )

# -------------------------

# 程式進入點

# -------------------------

def main():
    parser = build_arg_parser()
    args = parser.parse_args()

    items: List[str] = []
    items.extend(load_targets_from_files(args.in_files))
    items.extend(parse_targets_inline(args.targets_inline))

    seen = set()
    uniq: List[Tuple[str, str]] = []
    for x in items:
        t, k = classify_target(x)
        if k == "invalid":
            print(f"[略過] 非合法 domain/IP: {x}", file=sys.stderr)
            continue
        key = (t, k)
        if key not in seen:
            seen.add(key)
        uniq.append(key)

    if not uniq:
        print("[錯誤] 沒有可查詢的目標。請用 --in 或 --targets 輸入。", file=sys.stderr)
        sys.exit(2)
    provider = args.provider
    max_wait = int(args.timeout)
    if provider == "hetrixtools":
        token = os.getenv("HETRIX_API_TOKEN", "").strip()
        client = HetrixToolsClient(token)
    elif provider == "rbltracker":
        sid = os.getenv("RBLTRACKER_SID", "").strip()
        tok = os.getenv("RBLTRACKER_TOKEN", "").strip()
        client = RBLTrackerClient(sid, tok)
    else:
        print(f"[錯誤] 不支援的 provider: {provider}", file=sys.stderr)
        sys.exit(2)

    results: List[RBLResult] = []
    total = len(uniq)
    done = 0
    print(f"[資訊] Provider={provider}, 總計 {total} 項，並行={args.concurrency}，timeout={max_wait}s …")
    with cf.ThreadPoolExecutor(max_workers=args.concurrency) as ex:
        futs = [ex.submit(query_one, provider, client, target, kind, max_wait) for (target, kind) in uniq]
        for fut in cf.as_completed(futs):
            res = fut.result()
            results.append(res)
            done += 1
            print(f"  進度：{done}/{total} 完成", end="\r")
        print("\n[完成] 全部查詢已完成。")

        exporter = resolve_output_exporter(args.out_path)
        exporter(results, args.out_path)

        if args.do_print:
            print_summary(results)


if __name__ == "__main__":
    main()
