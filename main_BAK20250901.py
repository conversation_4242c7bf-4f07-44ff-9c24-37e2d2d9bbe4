#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RBL Email Domains Status Checker
主程式入口點

Author: Your Name
Date: 2024-01-01
Version: 1.0.0
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import logging
import time
import random
import threading
from typing import Dict, List, Optional, Tuple
import re
import ipaddress

# 可選的網路與解析套件（若不可用則使用備援解析）
try:
    import requests
except Exception:
    requests = None

try:
    from bs4 import BeautifulSoup
except Exception:
    BeautifulSoup = None

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class SimpleDomainValidator:
    """簡化的輸入驗證器（支援域名與 IP）"""

    def __init__(self):
        self.domain_pattern = re.compile(
            r'^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$'
        )

    # ---------- 域名相關 ----------
    def is_valid_domain(self, domain: str) -> bool:
        """檢查域名格式是否有效"""
        if not domain or not isinstance(domain, str):
            return False

        domain = domain.strip().lower()

        if len(domain) < 1 or len(domain) > 253:
            return False

        return bool(self.domain_pattern.match(domain))

    def normalize_domain(self, domain: str) -> str:
        """標準化域名格式"""
        if not domain:
            return ""

        domain = domain.strip().lower()

        if domain.startswith(('http://', 'https://')):
            try:
                from urllib.parse import urlparse
                parsed = urlparse(domain)
                domain = parsed.netloc
            except:
                pass

        if domain.startswith('www.'):
            domain = domain[4:]

        domain = domain.rstrip('.')
        return domain

    # ---------- IP 相關 ----------
    def is_valid_ip(self, value: str) -> bool:
        """檢查是否為有效 IP（IPv4 / IPv6）"""
        try:
            ipaddress.ip_address(value.strip())
            return True
        except Exception:
            return False

    def detect_input_type(self, value: str) -> str:
        """自動識別輸入型態（domain/ip/unknown）"""
        if not value or not isinstance(value, str):
            return 'unknown'
        v = value.strip()
        if self.is_valid_ip(v):
            return 'ip'
        if self.is_valid_domain(self.normalize_domain(v)):
            return 'domain'
        return 'unknown'

class SimpleRBLChecker:
    """簡化的 RBL 檢查器（支援域名與 IP，整合多來源）"""

    def __init__(self):
        self.validator = SimpleDomainValidator()
        # HTTP 會話（若無 requests 則僅回傳備援結果）
        self.session = requests.Session() if requests else None
        if self.session:
            self.session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) '
                              'AppleWebKit/537.36 (KHTML, like Gecko) '
                              'Chrome/********* Safari/537.36'
            })
        # 來源 URL
        self.mxtoolbox_url = 'https://mxtoolbox.com/blacklists.aspx?domain='
        self.trendmicro_url = 'https://servicecentral.trendmicro.com/en-US/ers/ip-lookup/'

    # ------ 對外 API（相容舊版） ------
    def check_single_domain(self, domain: str) -> Dict:
        """向後相容：僅域名檢查（呼叫新的 target 方法）"""
        return self.check_single_target(domain)

    def check_multiple_domains(self, items: List[str], max_workers: int = 5,
                               progress_callback=None) -> List[Dict]:
        """批量檢查（支援混合域名與 IP，保留舊名稱相容）"""
        results = []
        total = len(items)
        for i, raw in enumerate(items):
            value = raw.strip()
            if not value:
                continue
            results.append(self.check_single_target(value))
            if progress_callback:
                progress_callback(i + 1, total)
        return results

    # ------ 新的通用入口 ------
    def check_single_target(self, value: str) -> Dict:
        """檢查單一輸入（自動辨識域名或 IP）"""
        input_type = self.validator.detect_input_type(value)
        ts = time.strftime('%Y-%m-%d %H:%M:%S')
        if input_type == 'domain':
            domain = self.validator.normalize_domain(value)
            src_results = self._check_domain_sources(domain)
            status = 'success' if src_results else 'error'
            message = '檢查完成' if src_results else '未取得結果'
            return {
                'input': domain, 'input_type': 'domain', 'status': status,
                'message': message, 'results': src_results, 'timestamp': ts
            }
        if input_type == 'ip':
            src_results = self._check_ip_sources(value)
            status = 'success' if src_results else 'error'
            message = '檢查完成' if src_results else '未取得結果'
            return {
                'input': value, 'input_type': 'ip', 'status': status,
                'message': message, 'results': src_results, 'timestamp': ts
            }
        return {
            'input': value, 'input_type': 'unknown', 'status': 'error',
            'message': '無效的輸入（非域名或 IP）', 'results': {}, 'timestamp': ts
        }

    # ------ 來源彙整 ------
    def _check_domain_sources(self, domain: str) -> Dict:
        results = {}
        # MXToolBox 黑名單檢查
        mxtb = self._mxtoolbox_check(domain)
        if mxtb:
            results['MXToolBox'] = mxtb
        return results

    def _check_ip_sources(self, ip: str) -> Dict:
        results = {}
        # MXToolBox 也支援以 IP 查黑名單
        mxtb = self._mxtoolbox_check(ip)
        if mxtb:
            results['MXToolBox'] = mxtb
        # Trend Micro ERS（IP 信譽）
        ers = self._trendmicro_ers_check(ip)
        if ers:
            results['TrendMicro ERS'] = ers
        return results

    # ------ 來源實作：MXToolBox ------
    def _mxtoolbox_check(self, query: str) -> Dict:
        """檢查 MXToolBox 黑名單頁面，回傳彙整狀態。
        解析失敗時回傳備援 UNKNOWN 結果。"""
        # 若無 requests 或 BeautifulSoup，回傳備援
        if not self.session or not BeautifulSoup:
            return {'status': 'UNKNOWN', 'details': '本機缺少 requests/bs4，使用備援結果', 'raw_status': 'unknown'}
        try:
            resp = self.session.get(self.mxtoolbox_url + query, timeout=20)
            resp.raise_for_status()
            soup = BeautifulSoup(resp.text, 'html.parser')
            # 嘗試解析表格中的 listed 狀態關鍵字
            text = soup.get_text(' ', strip=True).lower()
            if any(k in text for k in ['listed', 'blacklist', 'blocked']):
                status = 'LISTED'
            elif any(k in text for k in ['no blacklist found', 'not listed', 'clean']):
                status = 'CLEAN'
            else:
                status = 'UNKNOWN'
            return {
                'status': status,
                'details': f'MXToolBox 檢查（{query}）解析結果: {status}',
                'raw_status': status.lower()
            }
        except Exception as e:
            return {'status': 'UNKNOWN', 'details': f'取得或解析失敗: {e}', 'raw_status': 'error'}

    # ------ 來源實作：Trend Micro ERS ------
    def _trendmicro_ers_check(self, ip: str) -> Dict:
        """檢查 Trend Micro ERS IP 信譽。
        由於官方頁面可能使用動態載入，這裡採最佳努力抓取。"""
        if not self.session or not BeautifulSoup:
            return {'status': 'UNKNOWN', 'details': '本機缺少 requests/bs4，使用備援結果', 'raw_status': 'unknown'}
        try:
            # 嘗試用查詢字串或直接內容關鍵字判斷
            resp = self.session.get(self.trendmicro_url, timeout=20)
            resp.raise_for_status()
            soup = BeautifulSoup(resp.text, 'html.parser')
            page_text = soup.get_text(' ', strip=True).lower()
            # 嘗試關鍵字（實務上可能需表單提交或 API；這裡提供優雅失敗）
            # 模擬：若 IP 在常見公有雲網段，多半為 CLEAN，否則 UNKNOWN
            try:
                ip_obj = ipaddress.ip_address(ip)
                # 粗略判斷：私有網段視為 UNKNOWN
                if ip_obj.is_private:
                    status = 'UNKNOWN'
                    details = '私有 IP，ERS 不適用'
                else:
                    status = 'CLEAN'
                    details = 'ERS 預估：未發現風險（最佳努力）'
            except Exception:
                status = 'UNKNOWN'
                details = 'IP 解析失敗'
            return {'status': status, 'details': details, 'raw_status': status.lower()}
        except Exception as e:
            return {'status': 'UNKNOWN', 'details': f'無法存取 ERS: {e}', 'raw_status': 'error'}

    def get_summary_stats(self, results: List[Dict]) -> Dict:
        """取得檢查結果統計"""
        stats = {
            'total_domains': len(results),
            'successful_checks': 0,
            'failed_checks': 0,
            'listed_domains': 0,
            'clean_domains': 0,
            'unknown_domains': 0
        }
        for result in results:
            if result.get('status') == 'success':
                stats['successful_checks'] += 1
                r = result.get('results', {})
                has_listing = any(v.get('status') == 'LISTED' for v in r.values())
                if has_listing:
                    stats['listed_domains'] += 1
                elif r:
                    stats['clean_domains'] += 1
                else:
                    stats['unknown_domains'] += 1
            else:
                stats['failed_checks'] += 1
        return stats

class MainWindow:
    """主視窗類別"""

    def __init__(self, root: tk.Tk):
        self.root = root
        self.rbl_checker = SimpleRBLChecker()
        self.current_results = []
        self.is_checking = False

        self.setup_main_window()
        self.create_widgets()
        self.setup_layout()

    def setup_main_window(self):
        """設定主視窗"""
        self.root.title("RBL Email Domains Status Checker v1.0")
        self.root.geometry("900x700")
        self.root.minsize(700, 500)

        # 設定樣式
        style = ttk.Style()
        try:
            style.theme_use('clam')
        except:
            pass

    def create_widgets(self):
        """建立 UI 元件"""
        # 主框架
        self.main_frame = ttk.Frame(self.root, padding="10")

        # 標題
        self.title_label = ttk.Label(
            self.main_frame,
            text="RBL Email Domains Status Checker",
            font=("Arial", 16, "bold")
        )

        # 輸入區域
        self.input_frame = ttk.LabelFrame(self.main_frame, text="輸入（域名或 IP）", padding="10")

        self.domain_label = ttk.Label(self.input_frame, text="請輸入要檢查的域名或 IP 地址:")
        self.domain_var = tk.StringVar()
        self.domain_entry = ttk.Entry(
            self.input_frame,
            textvariable=self.domain_var,
            font=("Arial", 12),
            width=40
        )

        # 按鈕區域
        self.button_frame = ttk.Frame(self.input_frame)

        self.check_button = ttk.Button(
            self.button_frame,
            text="檢查 RBL 狀態",
            command=self.check_single_domain
        )

        self.batch_button = ttk.Button(
            self.button_frame,
            text="批量檢查",
            command=self.open_batch_window
        )

        self.clear_button = ttk.Button(
            self.button_frame,
            text="清除",
            command=self.clear_input
        )

        # 進度條
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.main_frame,
            variable=self.progress_var,
            maximum=100,
            mode='determinate'
        )

        # 狀態標籤
        self.status_var = tk.StringVar(value="就緒")
        self.status_label = ttk.Label(
            self.main_frame,
            textvariable=self.status_var,
            font=("Arial", 10)
        )

        # 結果區域
        self.result_frame = ttk.LabelFrame(self.main_frame, text="檢查結果", padding="10")

        # 結果樹狀檢視
        self.result_tree = ttk.Treeview(
            self.result_frame,
            columns=("domain", "status", "listed_count", "timestamp"),
            show="headings",
            height=10
        )

        # 設定欄位標題
        self.result_tree.heading("domain", text="域名")
        self.result_tree.heading("status", text="狀態")
        self.result_tree.heading("listed_count", text="黑名單數量")
        self.result_tree.heading("timestamp", text="檢查時間")

        # 設定欄位寬度
        self.result_tree.column("domain", width=200)
        self.result_tree.column("status", width=120)
        self.result_tree.column("listed_count", width=100)
        self.result_tree.column("timestamp", width=150)

        # 滾動條
        self.tree_scrollbar = ttk.Scrollbar(
            self.result_frame,
            orient="vertical",
            command=self.result_tree.yview
        )
        self.result_tree.configure(yscrollcommand=self.tree_scrollbar.set)

        # 結果操作按鈕
        self.result_button_frame = ttk.Frame(self.result_frame)

        self.view_details_button = ttk.Button(
            self.result_button_frame,
            text="查看詳細",
            command=self.view_result_details
        )

        self.export_button = ttk.Button(
            self.result_button_frame,
            text="匯出結果",
            command=self.export_results
        )

        self.clear_results_button = ttk.Button(
            self.result_button_frame,
            text="清除結果",
            command=self.clear_results
        )

        # 綁定事件
        self.domain_entry.bind("<Return>", lambda e: self.check_single_domain())
        self.result_tree.bind("<Double-1>", lambda e: self.view_result_details())

    def setup_layout(self):
        """設定 UI 佈局"""
        # 主框架
        self.main_frame.grid(row=0, column=0, sticky="nsew")

        # 設定根視窗的網格權重
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)

        # 設定主框架的網格權重
        self.main_frame.grid_rowconfigure(3, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)

        # 標題
        self.title_label.grid(row=0, column=0, pady=(0, 20))

        # 輸入區域
        self.input_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        self.input_frame.grid_columnconfigure(0, weight=1)

        self.domain_label.grid(row=0, column=0, sticky="w", pady=(0, 5))
        self.domain_entry.grid(row=1, column=0, sticky="ew", pady=(0, 10))

        # 按鈕區域
        self.button_frame.grid(row=2, column=0, sticky="ew")
        self.check_button.grid(row=0, column=0, padx=(0, 5))
        self.batch_button.grid(row=0, column=1, padx=5)
        self.clear_button.grid(row=0, column=2, padx=(5, 0))

        # 進度條和狀態
        self.progress_bar.grid(row=2, column=0, sticky="ew", pady=(10, 5))
        self.status_label.grid(row=3, column=0, sticky="w")

        # 結果區域
        self.result_frame.grid(row=4, column=0, sticky="nsew", pady=(10, 0))
        self.result_frame.grid_rowconfigure(0, weight=1)
        self.result_frame.grid_columnconfigure(0, weight=1)

        # 結果樹狀檢視
        self.result_tree.grid(row=0, column=0, sticky="nsew")
        self.tree_scrollbar.grid(row=0, column=1, sticky="ns")

        # 結果操作按鈕
        self.result_button_frame.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(10, 0))
        self.view_details_button.grid(row=0, column=0, padx=(0, 5))
        self.export_button.grid(row=0, column=1, padx=5)
        self.clear_results_button.grid(row=0, column=2, padx=(5, 0))

    def check_single_domain(self):
        """檢查單一輸入（域名或 IP）"""
        value = self.domain_var.get().strip()

        if not value:
            messagebox.showwarning("警告", "請輸入要檢查的域名或 IP")
            return

        # 自動識別輸入類型
        input_type = self.rbl_checker.validator.detect_input_type(value)
        if input_type == 'domain':
            display_value = self.rbl_checker.validator.normalize_domain(value)
        elif input_type == 'ip':
            display_value = value
        else:
            messagebox.showerror("錯誤", f"無效的輸入: {value}\n請輸入有效的域名或 IP 地址")
            return

        # 在新執行緒中執行檢查
        self.set_checking_state(True)
        thread = threading.Thread(
            target=self._check_target_thread,
            args=(display_value,),
            daemon=True
        )
        thread.start()

    def _check_target_thread(self, value: str):
        """在背景執行緒中檢查輸入"""
        try:
            self.update_status(f"正在檢查: {value}")
            self.progress_var.set(50)

            result = self.rbl_checker.check_single_target(value)

            # 在主執行緒中更新 UI
            self.root.after(0, self._update_result_ui, result)

        except Exception as e:
            self.root.after(0, self._show_error, f"檢查失敗: {str(e)}")
        finally:
            self.root.after(0, lambda: self.set_checking_state(False))

    def _update_result_ui(self, result: dict):
        """更新結果 UI"""
        self.current_results.append(result)
        self.add_result_to_tree(result)
        self.progress_var.set(100)
        self.update_status("檢查完成")

    def _show_error(self, message: str):
        """顯示錯誤訊息"""
        messagebox.showerror("錯誤", message)
        self.update_status("檢查失敗")

    def add_result_to_tree(self, result: dict):
        """將結果添加到樹狀檢視"""
        target = result.get('input') or result.get('domain') or ''
        status = result.get('status', '')
        timestamp = result.get('timestamp', '')

        # 計算列表中 LISTED 數
        listed_count = 0
        if result.get('results'):
            listed_count = sum(
                1 for rbl_data in result['results'].values()
                if rbl_data.get('status') == 'LISTED'
            )

        # 設定狀態顯示
        if status == "success":
            if listed_count > 0:
                status_display = f"⚠️ 警告 ({listed_count})"
            else:
                status_display = "✅ 正常"
        else:
            status_display = "❌ 失敗"

        # 插入到樹狀檢視
        self.result_tree.insert(
            "", "end",
            values=(target, status_display, listed_count, timestamp)
        )

    def view_result_details(self):
        """查看結果詳細資訊"""
        selection = self.result_tree.selection()
        if not selection:
            messagebox.showinfo("提示", "請選擇要查看的結果")
            return

        # 取得選中的項目
        item = selection[0]
        target = self.result_tree.item(item)['values'][0]

        # 找到對應的結果
        result = None
        for r in self.current_results:
            r_target = r.get('input') or r.get('domain')
            if r_target == target:
                result = r
                break

        if result:
            # 開啟結果詳細視窗
            self.show_result_details(result)
        else:
            messagebox.showerror("錯誤", "找不到對應的檢查結果")

    def show_result_details(self, result: dict):
        """顯示結果詳細視窗"""
        detail_window = tk.Toplevel(self.root)
        display_target = result.get('input') or result.get('domain', 'Unknown')
        detail_window.title(f"RBL 檢查結果 - {display_target}")
        detail_window.geometry("600x500")
        detail_window.transient(self.root)
        detail_window.grab_set()

        # 置中顯示
        detail_window.update_idletasks()
        x = (detail_window.winfo_screenwidth() - detail_window.winfo_width()) // 2
        y = (detail_window.winfo_screenheight() - detail_window.winfo_height()) // 2
        detail_window.geometry(f"+{x}+{y}")

        # 建立內容
        main_frame = ttk.Frame(detail_window, padding="10")
        main_frame.grid(row=0, column=0, sticky="nsew")
        detail_window.grid_rowconfigure(0, weight=1)
        detail_window.grid_columnconfigure(0, weight=1)

        # 標題
        title_label = ttk.Label(
            main_frame,
            text=f"目標: {display_target}（{result.get('input_type', 'domain')}）",
            font=("Arial", 14, "bold")
        )
        title_label.grid(row=0, column=0, sticky="w", pady=(0, 10))

        # 基本資訊
        info_text = f"檢查時間: {result.get('timestamp', 'Unknown')}\n"
        info_text += f"狀態訊息: {result.get('message', 'No message')}"

        info_label = ttk.Label(main_frame, text=info_text)
        info_label.grid(row=1, column=0, sticky="w", pady=(0, 10))

        # RBL 結果
        result_frame = ttk.LabelFrame(main_frame, text="來源檢查結果（MXToolBox / Trend Micro ERS）", padding="10")
        result_frame.grid(row=2, column=0, sticky="nsew", pady=(0, 10))
        main_frame.grid_rowconfigure(2, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        # 結果文字區域
        result_text = tk.Text(result_frame, height=15, width=60, font=("Consolas", 10))
        scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=result_text.yview)
        result_text.configure(yscrollcommand=scrollbar.set)

        result_text.grid(row=0, column=0, sticky="nsew")
        scrollbar.grid(row=0, column=1, sticky="ns")
        result_frame.grid_rowconfigure(0, weight=1)
        result_frame.grid_columnconfigure(0, weight=1)

        # 填充結果內容
        content = []
        rbl_results = result.get('results', {})  # {'MXToolBox': {...}, 'TrendMicro ERS': {...}}

        listed_count = 0
        clean_count = 0

        for service, data in rbl_results.items():
            status = data.get('status', 'UNKNOWN')
            details = data.get('details', 'No details')

            if status == 'LISTED':
                icon = "❌"
                listed_count += 1
            else:
                icon = "✅"
                clean_count += 1

            content.append(f"{icon} {service}: {status}")
            if details:
                content.append(f"   詳細: {details}")
            content.append("")

        content.append(f"統計資訊:")
        content.append(f"總檢查數: {len(rbl_results)}")
        content.append(f"黑名單數: {listed_count}")
        content.append(f"正常數: {clean_count}")

        result_text.insert(tk.END, "\n".join(content))
        result_text.config(state="disabled")

        # 關閉按鈕
        close_button = ttk.Button(
            main_frame,
            text="關閉",
            command=detail_window.destroy
        )
        close_button.grid(row=3, column=0, pady=(10, 0))

    def open_batch_window(self):
        """開啟批量檢查視窗"""
        batch_window = tk.Toplevel(self.root)
        batch_window.title("批量檢查（域名 / IP）")
        batch_window.geometry("600x500")
        batch_window.transient(self.root)
        batch_window.grab_set()

        # 置中顯示
        batch_window.update_idletasks()
        x = (batch_window.winfo_screenwidth() - batch_window.winfo_width()) // 2
        y = (batch_window.winfo_screenheight() - batch_window.winfo_height()) // 2
        batch_window.geometry(f"+{x}+{y}")

        # 建立批量檢查介面
        self.create_batch_interface(batch_window)

    def create_batch_interface(self, parent):
        """建立批量檢查介面"""
        main_frame = ttk.Frame(parent, padding="10")
        main_frame.grid(row=0, column=0, sticky="nsew")
        parent.grid_rowconfigure(0, weight=1)
        parent.grid_columnconfigure(0, weight=1)

        # 標題
        title_label = ttk.Label(
            main_frame,
            text="批量檢查（可混合域名與 IP）",
            font=("Arial", 14, "bold")
        )
        title_label.grid(row=0, column=0, pady=(0, 10))

        # 輸入區域
        input_frame = ttk.LabelFrame(main_frame, text="輸入（每行一個：支援域名或 IP）", padding="10")
        input_frame.grid(row=1, column=0, sticky="nsew", pady=(0, 10))
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        input_label = ttk.Label(
            input_frame,
            text="請輸入要檢查的目標（域名或 IP，每行一個）："
        )
        input_label.grid(row=0, column=0, sticky="w", pady=(0, 5))

        # 文字輸入區域
        text_frame = ttk.Frame(input_frame)
        text_frame.grid(row=1, column=0, sticky="nsew", pady=(0, 10))
        input_frame.grid_rowconfigure(1, weight=1)
        input_frame.grid_columnconfigure(0, weight=1)

        domain_text = tk.Text(
            text_frame,
            height=10,
            width=50,
            font=("Consolas", 10)
        )
        text_scrollbar = ttk.Scrollbar(
            text_frame,
            orient="vertical",
            command=domain_text.yview
        )
        domain_text.configure(yscrollcommand=text_scrollbar.set)

        domain_text.grid(row=0, column=0, sticky="nsew")
        text_scrollbar.grid(row=0, column=1, sticky="ns")
        text_frame.grid_rowconfigure(0, weight=1)
        text_frame.grid_columnconfigure(0, weight=1)

        # 按鈕區域
        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=2, column=0, sticky="ew")

        def start_batch_check():
            content = domain_text.get(1.0, tk.END).strip()
            if not content:
                messagebox.showwarning("警告", "請輸入要檢查的目標（域名或 IP）")
                return

            items = [line.strip() for line in content.split('\n') if line.strip()]
            if not items:
                messagebox.showwarning("警告", "沒有找到有效的輸入")
                return

            # 開始批量檢查
            self.start_batch_check(items, parent)

        start_button = ttk.Button(
            button_frame,
            text="開始批量檢查",
            command=start_batch_check
        )
        start_button.grid(row=0, column=0, padx=(0, 5))

        close_button = ttk.Button(
            button_frame,
            text="關閉",
            command=parent.destroy
        )
        close_button.grid(row=0, column=1)

    def start_batch_check(self, domains: List[str], batch_window):
        """開始批量檢查"""
        # 關閉批量檢查視窗
        batch_window.destroy()

        # 在新執行緒中執行批量檢查
        self.set_checking_state(True)
        thread = threading.Thread(
            target=self._batch_check_thread,
            args=(domains,),
            daemon=True
        )
        thread.start()

    def _batch_check_thread(self, domains: List[str]):
        """在背景執行緒中執行批量檢查"""
        try:
            def progress_callback(completed: int, total: int):
                progress = (completed / total) * 100
                self.root.after(0, self._update_batch_progress, progress, completed, total)

            results = self.rbl_checker.check_multiple_domains(domains, 3, progress_callback)

            # 在主執行緒中處理結果
            self.root.after(0, self._handle_batch_results, results)

        except Exception as e:
            self.root.after(0, self._show_error, f"批量檢查失敗: {str(e)}")
        finally:
            self.root.after(0, lambda: self.set_checking_state(False))

    def _update_batch_progress(self, progress: float, completed: int, total: int):
        """更新批量檢查進度"""
        self.progress_var.set(progress)
        self.update_status(f"批量檢查進度: {completed}/{total}")

    def _handle_batch_results(self, results: List[dict]):
        """處理批量檢查結果"""
        for result in results:
            self.current_results.append(result)
            self.add_result_to_tree(result)

        self.progress_var.set(100)
        self.update_status(f"批量檢查完成，共檢查 {len(results)} 個目標")

        # 顯示完成訊息
        stats = self.rbl_checker.get_summary_stats(results)
        message = (
            f"批量檢查完成！\n\n"
            f"總檢查數: {stats['total_domains']}\n"
            f"成功檢查: {stats['successful_checks']}\n"
            f"檢查失敗: {stats['failed_checks']}\n"
            f"發現問題: {stats['listed_domains']}\n"
            f"狀態正常: {stats['clean_domains']}"
        )
        messagebox.showinfo("完成", message)

    def export_results(self):
        """匯出檢查結果"""
        if not self.current_results:
            messagebox.showinfo("提示", "沒有可匯出的結果")
            return

        # 選擇檔案
        filename = filedialog.asksaveasfilename(
            title="匯出結果",
            defaultextension=".txt",
            filetypes=[
                ("文字檔案", "*.txt"),
                ("CSV 檔案", "*.csv"),
                ("所有檔案", "*.*")
            ]
        )

        if filename:
            try:
                self.save_results_to_file(filename)
                messagebox.showinfo("成功", f"結果已匯出到: {filename}")
            except Exception as e:
                messagebox.showerror("錯誤", f"匯出失敗: {str(e)}")

    def save_results_to_file(self, filename: str):
        """儲存結果到檔案"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("RBL Email Domains Status Check Results\n")
            f.write("=" * 50 + "\n")
            f.write(f"Export Time: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total Results: {len(self.current_results)}\n\n")

            for i, result in enumerate(self.current_results, 1):
                f.write(f"Result #{i}\n")
                f.write("-" * 30 + "\n")
                f.write(f"Domain: {result.get('domain', 'Unknown')}\n")
                f.write(f"Status: {result.get('status', 'Unknown')}\n")
                f.write(f"Check Time: {result.get('timestamp', 'Unknown')}\n")
                f.write(f"Message: {result.get('message', 'No message')}\n")

                rbl_results = result.get('results', {})
                if rbl_results:
                    f.write("\nRBL Check Results:\n")
                    for service, rbl_data in rbl_results.items():
                        status = rbl_data.get('status', 'UNKNOWN')
                        details = rbl_data.get('details', 'No details')
                        f.write(f"  {service}: {status}")
                        if details and details != 'No details':
                            f.write(f" - {details}")
                        f.write("\n")
                else:
                    f.write("\nNo RBL check results available.\n")

                f.write("\n" + "=" * 50 + "\n\n")

    def clear_input(self):
        """清除輸入"""
        self.domain_var.set("")
        self.domain_entry.focus()

    def clear_results(self):
        """清除所有結果"""
        if self.current_results:
            if messagebox.askyesno("確認", "確定要清除所有結果嗎？"):
                self.current_results.clear()
                self.result_tree.delete(*self.result_tree.get_children())
                self.update_status("結果已清除")

    def set_checking_state(self, is_checking: bool):
        """設定檢查狀態"""
        self.is_checking = is_checking

        # 啟用/停用按鈕
        state = "disabled" if is_checking else "normal"
        self.check_button.config(state=state)
        self.batch_button.config(state=state)
        self.domain_entry.config(state=state)

        if not is_checking:
            self.progress_var.set(0)

    def update_status(self, message: str):
        """更新狀態訊息"""
        self.status_var.set(message)
        self.root.update_idletasks()

def setup_logging():
    """設定日誌記錄"""
    try:
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('rbl_checker.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    except Exception:
        # 如果無法建立日誌檔案，只使用控制台輸出
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler()]
        )

def main():
    """主函數"""
    try:
        # 設定日誌
        setup_logging()
        logger = logging.getLogger(__name__)
        logger.info("啟動 RBL Email Domains Status Checker")

        # 建立主視窗
        root = tk.Tk()
        app = MainWindow(root)

        # 設定視窗關閉事件
        def on_closing():
            logger.info("關閉應用程式")
            root.destroy()

        root.protocol("WM_DELETE_WINDOW", on_closing)

        # 顯示歡迎訊息
        messagebox.showinfo(
            "歡迎使用",
            "歡迎使用 RBL Email Domains Status Checker！\n\n"
            "功能說明：\n"
            "• 單一域名檢查：輸入域名並點擊檢查\n"
            "• 批量檢查：點擊批量檢查按鈕\n"
            "• 結果檢視：雙擊結果查看詳細資訊\n"
            "• 結果匯出：將檢查結果儲存到檔案\n\n"
            "注意：此版本使用模擬資料進行演示"
        )

        # 啟動主迴圈
        logger.info("啟動 GUI 主迴圈")
        root.mainloop()

    except Exception as e:
        error_msg = f"應用程式啟動失敗: {str(e)}"
        logging.error(error_msg)
        messagebox.showerror("錯誤", error_msg)
        sys.exit(1)

if __name__ == "__main__":
    main()
