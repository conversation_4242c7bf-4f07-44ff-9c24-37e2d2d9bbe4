# ui.py
r.report_link or "",
r.error or "",
]
print(" ".join(trunc(c, w).ljust(w) for c, w in zip(row, colw)))
print()




def export_results_csv(results: List[RBLResult], out_path: str) -> None:
os.makedirs(os.path.dirname(out_path) or ".", exist_ok=True)
with open(out_path, "w", newline="", encoding="utf-8") as f:
writer = csv.DictWriter(
f,
fieldnames=list(asdict(results[0]).keys())
)
writer.writeheader()
for r in results:
writer.writerow(asdict(r))
print(f"[輸出] CSV 已寫入 {out_path}")




def export_results_json(results: List[RBLResult], out_path: str) -> None:
os.makedirs(os.path.dirname(out_path) or ".", exist_ok=True)
with open(out_path, "w", encoding="utf-8") as f:
json.dump([asdict(r) for r in results], f, ensure_ascii=False, indent=2)
print(f"[輸出] JSON 已寫入 {out_path}")




# -------------------------
# CLI 參數（由 main.py 呼叫亦可重用）
# -------------------------


def build_arg_parser() -> argparse.ArgumentParser:
p = argparse.ArgumentParser(
description=(
"RBL 黑名單批次查詢（支援 HetrixTools、RBLTracker；以 API 查詢域名與 IPv4），" \
"可讀取清單、手動輸入並輸出 CSV/JSON。"
)
)
p.add_argument("--provider", choices=["hetrixtools", "rbltracker"], default="hetrixtools",
help="選擇 API 提供者（預設 hetrixtools）")
p.add_argument("--in", dest="in_files", nargs="*", default=[],
help="匯入檔案路徑（支援 .txt/.csv/.tsv，取第一欄）")
p.add_argument("--targets", dest="targets_inline", default=None,
help="以逗號或空白分隔的一次性清單（domain 或 IPv4）")
p.add_argument("--out", dest="out_path", default="results.csv",
help="輸出檔案路徑（副檔名 .csv 或 .json）")
p.add_argument("--concurrency", type=int, default=4,
help="並行查詢數（避免太大以免被限速）")
p.add_argument("--timeout", type=int, default=300,
help="單一項目最多等待秒數（含輪詢），預設 300 秒")
p.add_argument("--print", dest="do_print", action="store_true",
help="查詢完成後在終端機列印摘要表格")
# API 認證：用環境變數最方便
# HetrixTools: HETRIX_API_TOKEN
# RBLTracker: RBLTRACKER_SID / RBLTRACKER_TOKEN
return p




def resolve_output_exporter(path: str):
ext = os.path.splitext(path)[1].lower()
if ext == ".json":
return export_results_json
return export_results_csv