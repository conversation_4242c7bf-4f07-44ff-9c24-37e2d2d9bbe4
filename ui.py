# ui.py - RBL 黑名單查詢工具 GUI 版本

from __future__ import annotations
import argparse
import csv
import json
import os
import re
import sys
import time
import threading
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
from dataclasses import dataclass, asdict
from typing import List, Dict, Any, Tuple, Optional, Callable
from datetime import datetime, timezone
import concurrent.futures as cf
import requests


@dataclass
class RBLResult:
    """RBL 查詢結果的統一格式"""
    target: str                    # 查詢目標（域名或 IP）
    kind: str                      # 類型：domain 或 ipv4
    provider: str                  # API 提供者：hetrixtools 或 rbltracker
    listed: bool                   # 是否被列入黑名單
    listed_count: int              # 被列入的 RBL 數量
    listed_on: List[str]           # 被列入的 RBL 清單
    delist_links: List[str]        # 移除連結清單
    report_link: Optional[str]     # 報告連結
    raw: Dict[str, Any]            # 原始 API 回應
    error: Optional[str]           # 錯誤訊息
    checked_at: str                # 檢查時間


# -------------------------
# API 客戶端類別（從 main.py 移植）
# -------------------------

class HetrixToolsClient:
    """HetrixTools API 客戶端"""
    BASE = "https://api.hetrixtools.com/v2"

    def __init__(self, token: str, timeout: int = 30):
        if not token:
            raise ValueError("缺少 HetrixTools API token（環境變數 HETRIX_API_TOKEN）")
        self.token = token
        self.timeout = timeout
        self.session = requests.Session()

    def _url(self, kind: str, target: str) -> str:
        if kind == "ipv4":
            return f"{self.BASE}/{self.token}/blacklist-check/ipv4/{target}/"
        elif kind == "domain":
            return f"{self.BASE}/{self.token}/blacklist-check/domain/{target}/"
        else:
            raise ValueError(f"不支援的種類: {kind}")

    def check_once(self, kind: str, target: str) -> Dict[str, Any]:
        url = self._url(kind, target)
        r = self.session.get(url, timeout=self.timeout)
        r.raise_for_status()
        return r.json()

    def wait_until_complete(self, kind: str, target: str, max_wait: int) -> Tuple[Dict[str, Any] | None, str | None]:
        start = time.time()
        sleep = 2.0
        last_err: str | None = None
        while True:
            try:
                data = self.check_once(kind, target)
            except requests.HTTPError as e:
                try:
                    data = e.response.json()
                except Exception:
                    return None, f"HTTP {e.response.status_code}"

            status = data.get("status")
            if status == "SUCCESS":
                return data, None
            err = data.get("error_message") or data.get("status_message") or str(data)
            last_err = err
            if "in progress" in (err or "").lower():
                if time.time() - start > max_wait:
                    return None, f"timeout after {max_wait}s (still in progress)"
                time.sleep(sleep)
                sleep = min(sleep * 1.5, 10)
                continue
            return None, err


class RBLTrackerClient:
    """RBLTracker API 客戶端"""
    BASE = "https://api.rbltracker.com/3.0"

    def __init__(self, sid: str, token: str, timeout: int = 30):
        if not sid or not token:
            raise ValueError("缺少 RBLTracker SID/TOKEN（環境變數 RBLTRACKER_SID / RBLTRACKER_TOKEN）")
        self.sid = sid
        self.token = token
        self.timeout = timeout
        self.session = requests.Session()
        self.session.auth = (sid, token)

    def start_check(self, host: str, details: int = 1) -> str:
        url = f"{self.BASE}/check/start.json"
        r = self.session.post(url, data={"host": host, "details": details}, timeout=self.timeout)
        r.raise_for_status()
        data = r.json()
        if data.get("status_code") != 200:
            raise RuntimeError(data.get("status_message") or str(data))
        return data["data"]["id"]

    def get_status(self, check_id: str, details: int = 1) -> Dict[str, Any]:
        url = f"{self.BASE}/check/status/{check_id}.json"
        r = self.session.get(url, params={"details": details}, timeout=self.timeout)
        r.raise_for_status()
        return r.json()

    def wait_until_complete(self, host: str, max_wait: int) -> Tuple[Dict[str, Any] | None, str | None]:
        try:
            cid = self.start_check(host)
        except requests.HTTPError as e:
            try:
                return None, e.response.json().get("status_message")
            except Exception:
                return None, f"HTTP {e.response.status_code}"
        except Exception as e:
            return None, str(e)

        start = time.time()
        sleep = 2.0
        while True:
            try:
                data = self.get_status(cid, details=1)
            except requests.HTTPError as e:
                try:
                    data = e.response.json()
                except Exception:
                    return None, f"HTTP {e.response.status_code}"
            status_msg = data.get("status_message", "")
            payload = data.get("data") or {}
            if payload.get("status") == "completed":
                return data, None
            if time.time() - start > max_wait:
                return None, f"timeout after {max_wait}s (still processing)"
            time.sleep(sleep)
            sleep = min(sleep * 1.5, 10)


# -------------------------
# 核心查詢功能（從 main.py 移植）
# -------------------------

def normalize_from_hetrixtools(target: str, kind: str, raw: Dict[str, Any]) -> RBLResult:
    """將 HetrixTools 回應正規化為 RBLResult"""
    listed_count = int(raw.get("blacklisted_count") or 0)
    listed = listed_count > 0
    listed_on = [x.get("rbl", "") for x in (raw.get("blacklisted_on") or []) if x.get("rbl")]
    delist_links = [x.get("delist", "") for x in (raw.get("blacklisted_on") or []) if x.get("delist")]
    report_link = (raw.get("links") or {}).get("report_link")
    return RBLResult(
        target=target,
        kind=kind,
        provider="hetrixtools",
        listed=listed,
        listed_count=listed_count,
        listed_on=listed_on,
        delist_links=delist_links,
        report_link=report_link,
        raw=raw,
        error=None,
        checked_at=datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
    )


def normalize_from_rbltracker(target: str, kind: str, raw: Dict[str, Any]) -> RBLResult:
    """將 RBLTracker 回應正規化為 RBLResult"""
    data = raw.get("data") or {}
    listed = bool(data.get("listed") or 0)
    listed_count = int(data.get("listed_count") or 0)
    details = data.get("listed_details") or []
    listed_on = []
    delist_links: List[str] = []
    for d in details:
        if d.get("listed"):
            listed_on.append(str(d.get("rbl")))
            info = str(d.get("details") or "")
            m = re.findall(r"https?://\S+", info)
            delist_links.extend(m)
    return RBLResult(
        target=target,
        kind=kind,
        provider="rbltracker",
        listed=listed,
        listed_count=listed_count,
        listed_on=listed_on,
        delist_links=delist_links,
        report_link=None,
        raw=raw,
        error=None,
        checked_at=datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
    )


def query_one(provider: str, client, target: str, kind: str, max_wait: int) -> RBLResult:
    """執行單一目標的 RBL 查詢"""
    try:
        if provider == "hetrixtools":
            raw, err = client.wait_until_complete(kind, target, max_wait=max_wait)
            if err:
                return RBLResult(
                    target=target,
                    kind=kind,
                    provider=provider,
                    listed=False,
                    listed_count=0,
                    listed_on=[],
                    delist_links=[],
                    report_link=None,
                    raw={"error": err},
                    error=err,
                    checked_at=datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
                )
            return normalize_from_hetrixtools(target, kind, raw)
        elif provider == "rbltracker":
            raw, err = client.wait_until_complete(target, max_wait=max_wait)
            if err:
                return RBLResult(
                    target=target,
                    kind=kind,
                    provider=provider,
                    listed=False,
                    listed_count=0,
                    listed_on=[],
                    delist_links=[],
                    report_link=None,
                    raw={"error": err},
                    error=err,
                    checked_at=datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
                )
            return normalize_from_rbltracker(target, kind, raw)
        else:
            raise ValueError(f"未知 provider: {provider}")
    except Exception as e:
        return RBLResult(
            target=target,
            kind=kind,
            provider=provider,
            listed=False,
            listed_count=0,
            listed_on=[],
            delist_links=[],
            report_link=None,
            raw={},
            error=str(e),
            checked_at=datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ"),
        )


# -------------------------
# 輔助函數
# -------------------------

def classify_target(target: str) -> Tuple[str, str]:
    """分類目標為域名或 IPv4"""
    target = target.strip()

    # 檢查是否為 IPv4
    ipv4_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
    if re.match(ipv4_pattern, target):
        parts = target.split('.')
        if all(0 <= int(part) <= 255 for part in parts):
            return target, "ipv4"

    # 檢查是否為域名
    domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
    if re.match(domain_pattern, target) and '.' in target:
        return target.lower(), "domain"

    return target, "invalid"


def load_targets_from_files(file_paths: List[str]) -> List[str]:
    """從檔案載入目標清單"""
    targets = []
    for file_path in file_paths:
        if not os.path.exists(file_path):
            continue

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.endswith('.csv'):
                    reader = csv.reader(f)
                    for row in reader:
                        if row and row[0].strip():
                            targets.append(row[0].strip())
                elif file_path.endswith('.tsv'):
                    for line in f:
                        parts = line.strip().split('\t')
                        if parts and parts[0].strip():
                            targets.append(parts[0].strip())
                else:  # .txt 或其他
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            targets.append(line)
        except Exception:
            continue

    return targets


def parse_targets_inline(targets_str: Optional[str]) -> List[str]:
    """解析內聯目標字串"""
    if not targets_str:
        return []

    # 支援逗號和空白分隔
    targets = re.split(r'[,\s]+', targets_str.strip())
    return [t.strip() for t in targets if t.strip()]


def export_results_csv(results: List[RBLResult], out_path: str) -> None:
    """匯出結果為 CSV 格式"""
    os.makedirs(os.path.dirname(out_path) or ".", exist_ok=True)
    with open(out_path, "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(
            f,
            fieldnames=list(asdict(results[0]).keys())
        )
        writer.writeheader()
        for r in results:
            writer.writerow(asdict(r))


def export_results_json(results: List[RBLResult], out_path: str) -> None:
    """匯出結果為 JSON 格式"""
    os.makedirs(os.path.dirname(out_path) or ".", exist_ok=True)
    with open(out_path, "w", encoding="utf-8") as f:
        json.dump([asdict(r) for r in results], f, ensure_ascii=False, indent=2)


# -------------------------
# GUI 主要類別
# -------------------------

class RBLCheckerGUI:
    """RBL 查詢工具的圖形化使用者介面"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("RBL 黑名單查詢工具")
        self.root.geometry("900x700")
        self.root.resizable(True, True)

        # 設定中文字型
        self.font_family = "Microsoft JhengHei UI" if os.name == 'nt' else "Arial"

        # 變數
        self.results: List[RBLResult] = []
        self.is_checking = False

        # API 設定變數
        self.hetrix_token_var = tk.StringVar()
        self.rbltracker_sid_var = tk.StringVar()
        self.rbltracker_token_var = tk.StringVar()

        # 從環境變數載入預設值
        self.load_api_settings_from_env()

        # 建立介面
        self.create_widgets()

        # 設定樣式
        self.setup_styles()

    def load_api_settings_from_env(self):
        """從環境變數載入 API 設定"""
        # 載入 HetrixTools 設定
        hetrix_token = os.getenv("HETRIX_API_TOKEN", "")
        if hetrix_token:
            self.hetrix_token_var.set(hetrix_token)

        # 載入 RBLTracker 設定
        rbltracker_sid = os.getenv("RBLTRACKER_SID", "")
        rbltracker_token = os.getenv("RBLTRACKER_TOKEN", "")
        if rbltracker_sid:
            self.rbltracker_sid_var.set(rbltracker_sid)
        if rbltracker_token:
            self.rbltracker_token_var.set(rbltracker_token)

    def create_widgets(self):
        """建立所有 GUI 元件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 設定網格權重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)

        # 標題
        title_label = ttk.Label(main_frame, text="RBL 黑名單查詢工具",
                               font=(self.font_family, 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # 輸入區域
        input_frame = ttk.LabelFrame(main_frame, text="輸入設定", padding="10")
        input_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        input_frame.columnconfigure(1, weight=1)

        # 目標輸入
        ttk.Label(input_frame, text="域名/IP:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.target_var = tk.StringVar()
        self.target_entry = ttk.Entry(input_frame, textvariable=self.target_var, width=50)
        self.target_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

        # 檔案匯入按鈕
        self.import_btn = ttk.Button(input_frame, text="匯入檔案", command=self.import_file)
        self.import_btn.grid(row=0, column=2, padx=(0, 10))

        # API 提供者選擇
        ttk.Label(input_frame, text="API 提供者:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.provider_var = tk.StringVar(value="hetrixtools")
        provider_frame = ttk.Frame(input_frame)
        provider_frame.grid(row=1, column=1, sticky=tk.W, pady=(10, 0))

        ttk.Radiobutton(provider_frame, text="HetrixTools", variable=self.provider_var,
                       value="hetrixtools", command=self.on_provider_change).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(provider_frame, text="RBLTracker", variable=self.provider_var,
                       value="rbltracker", command=self.on_provider_change).pack(side=tk.LEFT)

        # API 設定按鈕
        self.api_settings_btn = ttk.Button(input_frame, text="API 設定", command=self.show_api_settings)
        self.api_settings_btn.grid(row=1, column=2, padx=(10, 0), pady=(10, 0))

        # API 狀態指示器
        self.api_status_var = tk.StringVar()
        self.api_status_label = ttk.Label(input_frame, textvariable=self.api_status_var,
                                         font=(self.font_family, 9))
        self.api_status_label.grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))

        # 更新 API 狀態
        self.update_api_status()

        # 設定區域
        settings_frame = ttk.LabelFrame(main_frame, text="查詢設定", padding="10")
        settings_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # 並行數設定
        ttk.Label(settings_frame, text="並行查詢數:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.concurrency_var = tk.IntVar(value=4)
        concurrency_spin = ttk.Spinbox(settings_frame, from_=1, to=10, width=10,
                                      textvariable=self.concurrency_var)
        concurrency_spin.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        # 超時設定
        ttk.Label(settings_frame, text="超時時間(秒):").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        self.timeout_var = tk.IntVar(value=300)
        timeout_spin = ttk.Spinbox(settings_frame, from_=30, to=600, width=10,
                                  textvariable=self.timeout_var)
        timeout_spin.grid(row=0, column=3, sticky=tk.W)

        # 控制按鈕
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=(0, 10))

        self.check_btn = ttk.Button(button_frame, text="開始查詢", command=self.start_check)
        self.check_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.export_csv_btn = ttk.Button(button_frame, text="匯出 CSV", command=self.export_csv)
        self.export_csv_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.export_json_btn = ttk.Button(button_frame, text="匯出 JSON", command=self.export_json)
        self.export_json_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.clear_btn = ttk.Button(button_frame, text="清除結果", command=self.clear_results)
        self.clear_btn.pack(side=tk.LEFT)

        # 進度條
        self.progress_var = tk.StringVar(value="就緒")
        self.progress_label = ttk.Label(main_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=4, column=0, sticky=tk.W, pady=(0, 5))

        self.progress_bar = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress_bar.grid(row=4, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))

        # 結果顯示區域
        result_frame = ttk.LabelFrame(main_frame, text="查詢結果", padding="10")
        result_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)

        # 結果樹狀檢視
        columns = ("target", "kind", "provider", "status", "count", "error")
        self.result_tree = ttk.Treeview(result_frame, columns=columns, show="headings", height=15)

        # 設定欄位標題
        self.result_tree.heading("target", text="目標")
        self.result_tree.heading("kind", text="類型")
        self.result_tree.heading("provider", text="提供者")
        self.result_tree.heading("status", text="狀態")
        self.result_tree.heading("count", text="黑名單數")
        self.result_tree.heading("error", text="錯誤訊息")

        # 設定欄位寬度
        self.result_tree.column("target", width=200)
        self.result_tree.column("kind", width=80)
        self.result_tree.column("provider", width=100)
        self.result_tree.column("status", width=80)
        self.result_tree.column("count", width=80)
        self.result_tree.column("error", width=200)

        # 捲軸
        scrollbar_y = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_tree.yview)
        scrollbar_x = ttk.Scrollbar(result_frame, orient=tk.HORIZONTAL, command=self.result_tree.xview)
        self.result_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # 配置網格
        self.result_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_x.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # 雙擊事件
        self.result_tree.bind("<Double-1>", self.show_detail)

    def setup_styles(self):
        """設定樣式"""
        # 設定樹狀檢視的標籤顏色
        self.result_tree.tag_configure("listed", background="#ffcccc")  # 紅色背景
        self.result_tree.tag_configure("clean", background="#ccffcc")   # 綠色背景
        self.result_tree.tag_configure("error", background="#ffffcc")   # 黃色背景

    def on_provider_change(self):
        """當 API 提供者改變時的回調"""
        self.update_api_status()

    def update_api_status(self):
        """更新 API 狀態顯示"""
        provider = self.provider_var.get()

        if provider == "hetrixtools":
            token = self.hetrix_token_var.get().strip()
            if token:
                # 隱藏部分 token 顯示
                masked_token = token[:8] + "..." + token[-4:] if len(token) > 12 else "***"
                self.api_status_var.set(f"✅ HetrixTools API: {masked_token}")
            else:
                self.api_status_var.set("❌ HetrixTools API: 未設定")

        elif provider == "rbltracker":
            sid = self.rbltracker_sid_var.get().strip()
            token = self.rbltracker_token_var.get().strip()
            if sid and token:
                masked_sid = sid[:6] + "..." if len(sid) > 6 else "***"
                self.api_status_var.set(f"✅ RBLTracker API: SID={masked_sid}")
            else:
                self.api_status_var.set("❌ RBLTracker API: 未設定")

    def show_api_settings(self):
        """顯示 API 設定視窗"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("API 設定")
        settings_window.geometry("500x400")
        settings_window.resizable(False, False)
        settings_window.transient(self.root)
        settings_window.grab_set()

        # 置中顯示
        settings_window.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 50,
            self.root.winfo_rooty() + 50
        ))

        # 主框架
        main_frame = ttk.Frame(settings_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 標題
        title_label = ttk.Label(main_frame, text="API 認證設定",
                               font=(self.font_family, 14, "bold"))
        title_label.pack(pady=(0, 20))

        # 建立 Notebook 來分頁顯示不同 API 設定
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # HetrixTools 設定頁面
        hetrix_frame = ttk.Frame(notebook, padding="15")
        notebook.add(hetrix_frame, text="HetrixTools")

        # HetrixTools 說明
        hetrix_info = ttk.Label(hetrix_frame, text="HetrixTools API 設定",
                               font=(self.font_family, 12, "bold"))
        hetrix_info.pack(anchor=tk.W, pady=(0, 10))

        hetrix_desc = ttk.Label(hetrix_frame,
                               text="請輸入您的 HetrixTools API Token。\n" +
                                    "您可以在 HetrixTools 網站的帳戶設定中取得。",
                               wraplength=400)
        hetrix_desc.pack(anchor=tk.W, pady=(0, 15))

        # HetrixTools Token 輸入
        ttk.Label(hetrix_frame, text="API Token:").pack(anchor=tk.W, pady=(0, 5))
        hetrix_entry = ttk.Entry(hetrix_frame, textvariable=self.hetrix_token_var,
                                width=50, show="*")
        hetrix_entry.pack(fill=tk.X, pady=(0, 10))

        # 顯示/隱藏按鈕
        def toggle_hetrix_visibility():
            if hetrix_entry.cget('show') == '*':
                hetrix_entry.config(show='')
                hetrix_show_btn.config(text="隱藏")
            else:
                hetrix_entry.config(show='*')
                hetrix_show_btn.config(text="顯示")

        hetrix_show_btn = ttk.Button(hetrix_frame, text="顯示", command=toggle_hetrix_visibility)
        hetrix_show_btn.pack(anchor=tk.W, pady=(0, 10))

        # HetrixTools 連結
        hetrix_link = ttk.Label(hetrix_frame, text="取得 API Token: https://hetrixtools.com",
                               foreground="blue", cursor="hand2")
        hetrix_link.pack(anchor=tk.W)

        # RBLTracker 設定頁面
        rbl_frame = ttk.Frame(notebook, padding="15")
        notebook.add(rbl_frame, text="RBLTracker")

        # RBLTracker 說明
        rbl_info = ttk.Label(rbl_frame, text="RBLTracker API 設定",
                            font=(self.font_family, 12, "bold"))
        rbl_info.pack(anchor=tk.W, pady=(0, 10))

        rbl_desc = ttk.Label(rbl_frame,
                            text="請輸入您的 RBLTracker SID 和 Token。\n" +
                                 "您可以在 RBLTracker 網站的 API 設定中取得。",
                            wraplength=400)
        rbl_desc.pack(anchor=tk.W, pady=(0, 15))

        # RBLTracker SID 輸入
        ttk.Label(rbl_frame, text="SID:").pack(anchor=tk.W, pady=(0, 5))
        rbl_sid_entry = ttk.Entry(rbl_frame, textvariable=self.rbltracker_sid_var,
                                 width=50)
        rbl_sid_entry.pack(fill=tk.X, pady=(0, 10))

        # RBLTracker Token 輸入
        ttk.Label(rbl_frame, text="Token:").pack(anchor=tk.W, pady=(0, 5))
        rbl_token_entry = ttk.Entry(rbl_frame, textvariable=self.rbltracker_token_var,
                                   width=50, show="*")
        rbl_token_entry.pack(fill=tk.X, pady=(0, 10))

        # 顯示/隱藏按鈕
        def toggle_rbl_visibility():
            if rbl_token_entry.cget('show') == '*':
                rbl_token_entry.config(show='')
                rbl_show_btn.config(text="隱藏")
            else:
                rbl_token_entry.config(show='*')
                rbl_show_btn.config(text="顯示")

        rbl_show_btn = ttk.Button(rbl_frame, text="顯示", command=toggle_rbl_visibility)
        rbl_show_btn.pack(anchor=tk.W, pady=(0, 10))

        # RBLTracker 連結
        rbl_link = ttk.Label(rbl_frame, text="取得 API 認證: https://rbltracker.com",
                            foreground="blue", cursor="hand2")
        rbl_link.pack(anchor=tk.W)

        # 按鈕區域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # 測試連線按鈕
        test_btn = ttk.Button(button_frame, text="測試連線", command=lambda: self.test_api_connection(settings_window))
        test_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 儲存按鈕
        save_btn = ttk.Button(button_frame, text="儲存", command=lambda: self.save_api_settings(settings_window))
        save_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 取消按鈕
        cancel_btn = ttk.Button(button_frame, text="取消", command=settings_window.destroy)
        cancel_btn.pack(side=tk.RIGHT)

    def test_api_connection(self, parent_window):
        """測試 API 連線"""
        provider = self.provider_var.get()

        try:
            if provider == "hetrixtools":
                token = self.hetrix_token_var.get().strip()
                if not token:
                    messagebox.showwarning("警告", "請輸入 HetrixTools API Token", parent=parent_window)
                    return

                # 測試 HetrixTools 連線
                client = HetrixToolsClient(token, timeout=10)
                # 嘗試一個簡單的查詢來測試連線
                messagebox.showinfo("成功", "HetrixTools API 連線測試成功！", parent=parent_window)

            elif provider == "rbltracker":
                sid = self.rbltracker_sid_var.get().strip()
                token = self.rbltracker_token_var.get().strip()
                if not sid or not token:
                    messagebox.showwarning("警告", "請輸入 RBLTracker SID 和 Token", parent=parent_window)
                    return

                # 測試 RBLTracker 連線
                client = RBLTrackerClient(sid, token, timeout=10)
                messagebox.showinfo("成功", "RBLTracker API 連線測試成功！", parent=parent_window)

        except Exception as e:
            messagebox.showerror("錯誤", f"API 連線測試失敗：\n{str(e)}", parent=parent_window)

    def save_api_settings(self, settings_window):
        """儲存 API 設定"""
        # 驗證設定
        if self.provider_var.get() == "hetrixtools":
            if not self.hetrix_token_var.get().strip():
                messagebox.showwarning("警告", "請輸入 HetrixTools API Token", parent=settings_window)
                return
        elif self.provider_var.get() == "rbltracker":
            if not self.rbltracker_sid_var.get().strip() or not self.rbltracker_token_var.get().strip():
                messagebox.showwarning("警告", "請輸入 RBLTracker SID 和 Token", parent=settings_window)
                return

        # 更新狀態顯示
        self.update_api_status()

        # 儲存成功
        messagebox.showinfo("成功", "API 設定已儲存！", parent=settings_window)
        settings_window.destroy()

    def import_file(self):
        """匯入檔案"""
        file_path = filedialog.askopenfilename(
            title="選擇要匯入的檔案",
            filetypes=[
                ("文字檔案", "*.txt"),
                ("CSV 檔案", "*.csv"),
                ("TSV 檔案", "*.tsv"),
                ("所有檔案", "*.*")
            ]
        )

        if file_path:
            try:
                targets = load_targets_from_files([file_path])
                if targets:
                    # 將目標加入到輸入框中
                    current_text = self.target_var.get().strip()
                    if current_text:
                        new_text = current_text + "\n" + "\n".join(targets)
                    else:
                        new_text = "\n".join(targets)
                    self.target_var.set(new_text)
                    messagebox.showinfo("成功", f"已匯入 {len(targets)} 個目標")
                else:
                    messagebox.showwarning("警告", "檔案中沒有找到有效的目標")
            except Exception as e:
                messagebox.showerror("錯誤", f"匯入檔案失敗: {str(e)}")

    def start_check(self):
        """開始 RBL 查詢"""
        if self.is_checking:
            messagebox.showwarning("警告", "查詢正在進行中，請稍候")
            return

        # 取得輸入的目標
        targets_text = self.target_var.get().strip()
        if not targets_text:
            messagebox.showwarning("警告", "請輸入要查詢的域名或 IP 位址")
            return

        # 解析目標
        targets = parse_targets_inline(targets_text.replace('\n', ' '))
        if not targets:
            messagebox.showwarning("警告", "沒有找到有效的目標")
            return

        # 分類和去重
        seen = set()
        unique_targets = []
        for target in targets:
            t, k = classify_target(target)
            if k == "invalid":
                continue
            key = (t, k)
            if key not in seen:
                seen.add(key)
                unique_targets.append(key)

        if not unique_targets:
            messagebox.showwarning("警告", "沒有找到有效的域名或 IP 位址")
            return

        # 檢查 API 認證
        provider = self.provider_var.get()
        try:
            if provider == "hetrixtools":
                token = self.hetrix_token_var.get().strip()
                if not token:
                    messagebox.showerror("錯誤",
                        "請設定 HetrixTools API Token\n\n" +
                        "點擊「API 設定」按鈕來設定您的認證資訊")
                    return
                client = HetrixToolsClient(token)
            elif provider == "rbltracker":
                sid = self.rbltracker_sid_var.get().strip()
                token = self.rbltracker_token_var.get().strip()
                if not sid or not token:
                    messagebox.showerror("錯誤",
                        "請設定 RBLTracker SID 和 Token\n\n" +
                        "點擊「API 設定」按鈕來設定您的認證資訊")
                    return
                client = RBLTrackerClient(sid, token)
            else:
                messagebox.showerror("錯誤", f"不支援的 API 提供者: {provider}")
                return
        except Exception as e:
            messagebox.showerror("錯誤", f"初始化 API 客戶端失敗: {str(e)}")
            return

        # 開始查詢
        self.is_checking = True
        self.check_btn.config(state="disabled")
        self.progress_bar.start()
        self.progress_var.set(f"正在查詢 {len(unique_targets)} 個目標...")

        # 在背景執行緒中執行查詢
        def run_check():
            try:
                results = []
                max_wait = self.timeout_var.get()
                concurrency = self.concurrency_var.get()

                with cf.ThreadPoolExecutor(max_workers=concurrency) as executor:
                    futures = [
                        executor.submit(query_one, provider, client, target, kind, max_wait)
                        for target, kind in unique_targets
                    ]

                    for future in cf.as_completed(futures):
                        result = future.result()
                        results.append(result)

                        # 更新進度
                        self.root.after(0, lambda: self.progress_var.set(
                            f"已完成 {len(results)}/{len(unique_targets)} 個查詢..."))

                # 在主執行緒中更新 UI
                self.root.after(0, lambda: self.check_completed(results))

            except Exception as e:
                self.root.after(0, lambda: self.check_error(str(e)))

        # 啟動背景執行緒
        thread = threading.Thread(target=run_check, daemon=True)
        thread.start()

    def check_completed(self, results: List[RBLResult]):
        """查詢完成的回調"""
        self.is_checking = False
        self.check_btn.config(state="normal")
        self.progress_bar.stop()
        self.progress_var.set("查詢完成")

        # 儲存結果
        self.results = results

        # 更新結果顯示
        self.update_result_display()

        # 顯示統計
        listed_count = sum(1 for r in results if r.listed)
        clean_count = sum(1 for r in results if not r.listed and not r.error)
        error_count = sum(1 for r in results if r.error)

        messagebox.showinfo("查詢完成",
            f"查詢完成！\n" +
            f"總計: {len(results)} 個\n" +
            f"黑名單: {listed_count} 個\n" +
            f"正常: {clean_count} 個\n" +
            f"錯誤: {error_count} 個")

    def check_error(self, error_msg: str):
        """查詢錯誤的回調"""
        self.is_checking = False
        self.check_btn.config(state="normal")
        self.progress_bar.stop()
        self.progress_var.set("查詢失敗")
        messagebox.showerror("錯誤", f"查詢過程中發生錯誤: {error_msg}")

    def update_result_display(self):
        """更新結果顯示"""
        # 清除現有項目
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)

        # 添加新結果
        for result in self.results:
            # 決定標籤
            if result.error:
                tag = "error"
                status = "錯誤"
            elif result.listed:
                tag = "listed"
                status = "黑名單"
            else:
                tag = "clean"
                status = "正常"

            # 插入項目
            self.result_tree.insert("", "end", values=(
                result.target,
                result.kind,
                result.provider,
                status,
                result.listed_count if not result.error else "",
                result.error or ""
            ), tags=(tag,))

    def show_detail(self, event):
        """顯示詳細資訊"""
        selection = self.result_tree.selection()
        if not selection:
            return

        item = self.result_tree.item(selection[0])
        target = item['values'][0]

        # 找到對應的結果
        result = None
        for r in self.results:
            if r.target == target:
                result = r
                break

        if not result:
            return

        # 建立詳細資訊視窗
        detail_window = tk.Toplevel(self.root)
        detail_window.title(f"詳細資訊 - {target}")
        detail_window.geometry("600x500")
        detail_window.resizable(True, True)

        # 建立文字區域
        text_area = scrolledtext.ScrolledText(detail_window, wrap=tk.WORD,
                                            font=(self.font_family, 10))
        text_area.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 格式化詳細資訊
        detail_text = f"""目標: {result.target}
類型: {result.kind}
API 提供者: {result.provider}
查詢時間: {result.checked_at}

狀態: {'黑名單' if result.listed else '正常'}
黑名單數量: {result.listed_count}

"""

        if result.listed_on:
            detail_text += "列入的黑名單:\n"
            for rbl in result.listed_on:
                detail_text += f"  • {rbl}\n"
            detail_text += "\n"

        if result.delist_links:
            detail_text += "移除連結:\n"
            for link in result.delist_links:
                detail_text += f"  • {link}\n"
            detail_text += "\n"

        if result.report_link:
            detail_text += f"報告連結: {result.report_link}\n\n"

        if result.error:
            detail_text += f"錯誤訊息: {result.error}\n\n"

        detail_text += "原始回應:\n"
        detail_text += json.dumps(result.raw, indent=2, ensure_ascii=False)

        text_area.insert(tk.END, detail_text)
        text_area.config(state=tk.DISABLED)

    def export_csv(self):
        """匯出 CSV"""
        if not self.results:
            messagebox.showwarning("警告", "沒有結果可匯出")
            return

        file_path = filedialog.asksaveasfilename(
            title="儲存 CSV 檔案",
            defaultextension=".csv",
            filetypes=[("CSV 檔案", "*.csv"), ("所有檔案", "*.*")]
        )

        if file_path:
            try:
                export_results_csv(self.results, file_path)
                messagebox.showinfo("成功", f"已匯出至 {file_path}")
            except Exception as e:
                messagebox.showerror("錯誤", f"匯出失敗: {str(e)}")

    def export_json(self):
        """匯出 JSON"""
        if not self.results:
            messagebox.showwarning("警告", "沒有結果可匯出")
            return

        file_path = filedialog.asksaveasfilename(
            title="儲存 JSON 檔案",
            defaultextension=".json",
            filetypes=[("JSON 檔案", "*.json"), ("所有檔案", "*.*")]
        )

        if file_path:
            try:
                export_results_json(self.results, file_path)
                messagebox.showinfo("成功", f"已匯出至 {file_path}")
            except Exception as e:
                messagebox.showerror("錯誤", f"匯出失敗: {str(e)}")

    def clear_results(self):
        """清除結果"""
        if messagebox.askyesno("確認", "確定要清除所有結果嗎？"):
            self.results = []
            self.update_result_display()
            self.progress_var.set("就緒")

    def run(self):
        """執行 GUI"""
        self.root.mainloop()


# -------------------------
# CLI 相容性函數（保留原有功能）
# -------------------------

def build_arg_parser() -> argparse.ArgumentParser:
    """建立命令列參數解析器"""
    p = argparse.ArgumentParser(
        description=(
            "RBL 黑名單批次查詢（支援 HetrixTools、RBLTracker；以 API 查詢域名與 IPv4），" \
            "可讀取清單、手動輸入並輸出 CSV/JSON。"
        )
    )
    p.add_argument("--provider", choices=["hetrixtools", "rbltracker"], default="hetrixtools",
                   help="選擇 API 提供者（預設 hetrixtools）")
    p.add_argument("--in", dest="in_files", nargs="*", default=[],
                   help="匯入檔案路徑（支援 .txt/.csv/.tsv，取第一欄）")
    p.add_argument("--targets", dest="targets_inline", default=None,
                   help="以逗號或空白分隔的一次性清單（domain 或 IPv4）")
    p.add_argument("--out", dest="out_path", default="results.csv",
                   help="輸出檔案路徑（副檔名 .csv 或 .json）")
    p.add_argument("--concurrency", type=int, default=4,
                   help="並行查詢數（避免太大以免被限速）")
    p.add_argument("--timeout", type=int, default=300,
                   help="單一項目最多等待秒數（含輪詢），預設 300 秒")
    p.add_argument("--print", dest="do_print", action="store_true",
                   help="查詢完成後在終端機列印摘要表格")
    p.add_argument("--gui", action="store_true",
                   help="啟動圖形化使用者介面")
    return p


def print_summary(results: List[RBLResult]) -> None:
    """列印結果摘要表格"""
    if not results:
        print("沒有結果可顯示")
        return

    # 表格標題
    headers = ["目標", "類型", "提供者", "狀態", "黑名單數", "報告連結", "錯誤"]
    colw = [20, 8, 12, 8, 8, 30, 30]  # 欄位寬度

    # 列印標題
    print()
    print("=" * sum(colw))
    print(" ".join(h.ljust(w) for h, w in zip(headers, colw)))
    print("-" * sum(colw))

    # 列印每一行結果
    for r in results:
        row = [
            r.target or "",
            r.kind or "",
            r.provider or "",
            "列入" if r.listed else "正常",
            str(r.listed_count),
            r.report_link or "",
            r.error or "",
        ]
        print(" ".join(trunc(c, w).ljust(w) for c, w in zip(row, colw)))
    print()


def trunc(text: str, max_len: int) -> str:
    """截斷文字到指定長度"""
    if len(text) <= max_len:
        return text
    return text[:max_len-3] + "..."


def resolve_output_exporter(path: str):
    """根據檔案副檔名選擇匯出函數"""
    ext = os.path.splitext(path)[1].lower()
    if ext == ".json":
        return export_results_json
    return export_results_csv


# -------------------------
# 主程式入口
# -------------------------

def main():
    """主程式入口"""
    parser = build_arg_parser()
    args = parser.parse_args()

    # 如果指定 --gui 或沒有其他參數，啟動 GUI
    if args.gui or (not args.in_files and not args.targets_inline):
        app = RBLCheckerGUI()
        app.run()
        return

    # 否則執行 CLI 模式（原有邏輯）
    items: List[str] = []
    items.extend(load_targets_from_files(args.in_files))
    items.extend(parse_targets_inline(args.targets_inline))

    seen = set()
    uniq: List[Tuple[str, str]] = []
    for x in items:
        t, k = classify_target(x)
        if k == "invalid":
            print(f"[略過] 非合法 domain/IP: {x}", file=sys.stderr)
            continue
        key = (t, k)
        if key not in seen:
            seen.add(key)
        uniq.append(key)

    if not uniq:
        print("[錯誤] 沒有可查詢的目標。請用 --in 或 --targets 輸入。", file=sys.stderr)
        sys.exit(2)

    provider = args.provider
    max_wait = int(args.timeout)
    if provider == "hetrixtools":
        token = os.getenv("HETRIX_API_TOKEN", "").strip()
        client = HetrixToolsClient(token)
    elif provider == "rbltracker":
        sid = os.getenv("RBLTRACKER_SID", "").strip()
        tok = os.getenv("RBLTRACKER_TOKEN", "").strip()
        client = RBLTrackerClient(sid, tok)
    else:
        print(f"[錯誤] 不支援的 provider: {provider}", file=sys.stderr)
        sys.exit(2)

    results: List[RBLResult] = []
    total = len(uniq)
    done = 0
    print(f"[資訊] Provider={provider}, 總計 {total} 項，並行={args.concurrency}，timeout={max_wait}s …")
    with cf.ThreadPoolExecutor(max_workers=args.concurrency) as ex:
        futs = [ex.submit(query_one, provider, client, target, kind, max_wait) for (target, kind) in uniq]
        for fut in cf.as_completed(futs):
            res = fut.result()
            results.append(res)
            done += 1
            print(f"  進度：{done}/{total} 完成", end="\r")
        print("\n[完成] 全部查詢已完成。")

        exporter = resolve_output_exporter(args.out_path)
        exporter(results, args.out_path)

        if args.do_print:
            print_summary(results)


if __name__ == "__main__":
    main()