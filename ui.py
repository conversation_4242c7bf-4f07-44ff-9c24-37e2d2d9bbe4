# ui.py

from __future__ import annotations
import argparse
import csv
import json
import os
import re
from dataclasses import dataclass, asdict
from typing import List, Dict, Any, Tuple, Optional


@dataclass
class RBLResult:
    """RBL 查詢結果的統一格式"""
    target: str                    # 查詢目標（域名或 IP）
    kind: str                      # 類型：domain 或 ipv4
    provider: str                  # API 提供者：hetrixtools 或 rbltracker
    listed: bool                   # 是否被列入黑名單
    listed_count: int              # 被列入的 RBL 數量
    listed_on: List[str]           # 被列入的 RBL 清單
    delist_links: List[str]        # 移除連結清單
    report_link: Optional[str]     # 報告連結
    raw: Dict[str, Any]            # 原始 API 回應
    error: Optional[str]           # 錯誤訊息
    checked_at: str                # 檢查時間


def trunc(text: str, max_len: int) -> str:
    """截斷文字到指定長度"""
    if len(text) <= max_len:
        return text
    return text[:max_len-3] + "..."


def classify_target(target: str) -> Tuple[str, str]:
    """分類目標為域名或 IPv4"""
    target = target.strip()

    # 檢查是否為 IPv4
    ipv4_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
    if re.match(ipv4_pattern, target):
        parts = target.split('.')
        if all(0 <= int(part) <= 255 for part in parts):
            return target, "ipv4"

    # 檢查是否為域名
    domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
    if re.match(domain_pattern, target) and '.' in target:
        return target.lower(), "domain"

    return target, "invalid"


def load_targets_from_files(file_paths: List[str]) -> List[str]:
    """從檔案載入目標清單"""
    targets = []
    for file_path in file_paths:
        if not os.path.exists(file_path):
            print(f"[警告] 檔案不存在: {file_path}")
            continue

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.endswith('.csv'):
                    reader = csv.reader(f)
                    for row in reader:
                        if row and row[0].strip():
                            targets.append(row[0].strip())
                elif file_path.endswith('.tsv'):
                    for line in f:
                        parts = line.strip().split('\t')
                        if parts and parts[0].strip():
                            targets.append(parts[0].strip())
                else:  # .txt 或其他
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            targets.append(line)
        except Exception as e:
            print(f"[錯誤] 讀取檔案 {file_path} 失敗: {e}")

    return targets


def parse_targets_inline(targets_str: Optional[str]) -> List[str]:
    """解析內聯目標字串"""
    if not targets_str:
        return []

    # 支援逗號和空白分隔
    targets = re.split(r'[,\s]+', targets_str.strip())
    return [t.strip() for t in targets if t.strip()]


def print_summary(results: List[RBLResult]) -> None:
    """列印結果摘要表格"""
    if not results:
        print("沒有結果可顯示")
        return

    # 表格標題
    headers = ["目標", "類型", "提供者", "狀態", "黑名單數", "報告連結", "錯誤"]
    colw = [20, 8, 12, 8, 8, 30, 30]  # 欄位寬度

    # 列印標題
    print()
    print("=" * sum(colw))
    print(" ".join(h.ljust(w) for h, w in zip(headers, colw)))
    print("-" * sum(colw))

    # 列印每一行結果
    for r in results:
        row = [
            r.target or "",
            r.kind or "",
            r.provider or "",
            "列入" if r.listed else "正常",
            str(r.listed_count),
            r.report_link or "",
            r.error or "",
        ]
        print(" ".join(trunc(c, w).ljust(w) for c, w in zip(row, colw)))
    print()


def export_results_csv(results: List[RBLResult], out_path: str) -> None:
    """匯出結果為 CSV 格式"""
    os.makedirs(os.path.dirname(out_path) or ".", exist_ok=True)
    with open(out_path, "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(
            f,
            fieldnames=list(asdict(results[0]).keys())
        )
        writer.writeheader()
        for r in results:
            writer.writerow(asdict(r))
    print(f"[輸出] CSV 已寫入 {out_path}")


def export_results_json(results: List[RBLResult], out_path: str) -> None:
    """匯出結果為 JSON 格式"""
    os.makedirs(os.path.dirname(out_path) or ".", exist_ok=True)
    with open(out_path, "w", encoding="utf-8") as f:
        json.dump([asdict(r) for r in results], f, ensure_ascii=False, indent=2)
    print(f"[輸出] JSON 已寫入 {out_path}")


# -------------------------
# CLI 參數（由 main.py 呼叫亦可重用）
# -------------------------


def build_arg_parser() -> argparse.ArgumentParser:
    """建立命令列參數解析器"""
    p = argparse.ArgumentParser(
        description=(
            "RBL 黑名單批次查詢（支援 HetrixTools、RBLTracker；以 API 查詢域名與 IPv4），" \
            "可讀取清單、手動輸入並輸出 CSV/JSON。"
        )
    )
    p.add_argument("--provider", choices=["hetrixtools", "rbltracker"], default="hetrixtools",
                   help="選擇 API 提供者（預設 hetrixtools）")
    p.add_argument("--in", dest="in_files", nargs="*", default=[],
                   help="匯入檔案路徑（支援 .txt/.csv/.tsv，取第一欄）")
    p.add_argument("--targets", dest="targets_inline", default=None,
                   help="以逗號或空白分隔的一次性清單（domain 或 IPv4）")
    p.add_argument("--out", dest="out_path", default="results.csv",
                   help="輸出檔案路徑（副檔名 .csv 或 .json）")
    p.add_argument("--concurrency", type=int, default=4,
                   help="並行查詢數（避免太大以免被限速）")
    p.add_argument("--timeout", type=int, default=300,
                   help="單一項目最多等待秒數（含輪詢），預設 300 秒")
    p.add_argument("--print", dest="do_print", action="store_true",
                   help="查詢完成後在終端機列印摘要表格")
    # API 認證：用環境變數最方便
    # HetrixTools: HETRIX_API_TOKEN
    # RBLTracker: RBLTRACKER_SID / RBLTRACKER_TOKEN
    return p


def resolve_output_exporter(path: str):
    """根據檔案副檔名選擇匯出函數"""
    ext = os.path.splitext(path)[1].lower()
    if ext == ".json":
        return export_results_json
    return export_results_csv