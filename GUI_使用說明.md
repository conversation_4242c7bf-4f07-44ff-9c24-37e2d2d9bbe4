# RBL 黑名單查詢工具 GUI 版本使用說明

## 概述

這是一個圖形化的 RBL（Real-time Blackhole List）黑名單查詢工具，支援批次查詢域名和 IP 位址是否被列入各種垃圾郵件黑名單。

## 功能特色

- 🖥️ **圖形化介面**: 直觀易用的 GUI 介面
- 🔍 **批次查詢**: 支援同時查詢多個域名或 IP
- 📁 **檔案匯入**: 從 TXT、CSV、TSV 檔案匯入目標清單
- 🌐 **多 API 支援**: 支援 HetrixTools 和 RBLTracker 兩種 API
- 📊 **詳細結果**: 顯示完整的黑名單檢查結果
- 💾 **結果匯出**: 支援匯出為 CSV 或 JSON 格式
- ⚡ **並行處理**: 支援多執行緒並行查詢

## 系統需求

- Python 3.9 或更高版本
- tkinter（通常隨 Python 安裝）
- requests 套件

## 安裝

1. 確保已安裝 Python 3.9+
2. 安裝必要套件：
   ```bash
   pip install requests
   ```

## 啟動方式

### 方式一：直接啟動 GUI
```bash
python ui.py --gui
```

### 方式二：使用啟動器
```bash
python gui_launcher.py
```

### 方式三：無參數啟動（自動進入 GUI 模式）
```bash
python ui.py
```

## API 設定

在使用前，您需要設定 API 認證資訊：

### HetrixTools API
1. 註冊 [HetrixTools](https://hetrixtools.com) 帳號
2. 取得 API Token
3. 設定環境變數：
   ```bash
   # Windows
   set HETRIX_API_TOKEN=your_api_token_here
   
   # Linux/Mac
   export HETRIX_API_TOKEN=your_api_token_here
   ```

### RBLTracker API
1. 註冊 [RBLTracker](https://rbltracker.com) 帳號
2. 取得 SID 和 Token
3. 設定環境變數：
   ```bash
   # Windows
   set RBLTRACKER_SID=your_sid_here
   set RBLTRACKER_TOKEN=your_token_here
   
   # Linux/Mac
   export RBLTRACKER_SID=your_sid_here
   export RBLTRACKER_TOKEN=your_token_here
   ```

## 使用方法

### 1. 輸入目標
- 在「域名/IP」欄位中輸入要查詢的域名或 IP 位址
- 支援多個目標，用空格、逗號或換行分隔
- 例如：`google.com, *******, microsoft.com`

### 2. 匯入檔案
- 點擊「匯入檔案」按鈕
- 選擇包含域名或 IP 的文字檔案（.txt、.csv、.tsv）
- 系統會自動讀取第一欄的內容

### 3. 選擇 API 提供者
- 選擇「HetrixTools」或「RBLTracker」
- 確保已設定對應的 API 認證

### 4. 調整設定
- **並行查詢數**: 同時進行的查詢數量（1-10）
- **超時時間**: 單一查詢的最大等待時間（30-600 秒）

### 5. 開始查詢
- 點擊「開始查詢」按鈕
- 查詢過程中會顯示進度
- 完成後會顯示統計結果

### 6. 查看結果
- 結果會顯示在下方的表格中
- 不同狀態用不同顏色標示：
  - 🟢 綠色：正常（未列入黑名單）
  - 🔴 紅色：黑名單（已列入黑名單）
  - 🟡 黃色：錯誤（查詢失敗）

### 7. 查看詳細資訊
- 雙擊任一結果行可查看詳細資訊
- 包含完整的黑名單清單、移除連結等

### 8. 匯出結果
- 點擊「匯出 CSV」或「匯出 JSON」
- 選擇儲存位置和檔名

## 檔案格式

### 匯入檔案格式
支援以下格式的檔案：

**TXT 檔案**：
```
google.com
*******
microsoft.com
```

**CSV 檔案**：
```
domain,description
google.com,Google DNS
*******,Cloudflare DNS
```

**TSV 檔案**：
```
domain	description
google.com	Google DNS
*******	Cloudflare DNS
```

### 匯出檔案格式
- **CSV**: 標準的逗號分隔值格式，可用 Excel 開啟
- **JSON**: 結構化的 JSON 格式，包含完整的查詢結果

## 命令列模式

除了 GUI 模式，工具也支援傳統的命令列模式：

```bash
# 查詢單一目標
python ui.py --targets "google.com"

# 從檔案批次查詢
python ui.py --in targets.txt --print

# 指定輸出格式
python ui.py --targets "google.com" --out results.json
```

## 故障排除

### 常見問題

1. **API 認證錯誤**
   - 確認環境變數設定正確
   - 檢查 API Token 是否有效

2. **網路連線問題**
   - 確認網路連線正常
   - 檢查防火牆設定

3. **GUI 無法啟動**
   - 確認已安裝 tkinter
   - Windows 通常內建，Linux 可能需要安裝：
     ```bash
     sudo apt-get install python3-tk
     ```

4. **查詢超時**
   - 增加超時時間設定
   - 減少並行查詢數

### 錯誤訊息

- **"缺少 API token"**: 請設定對應的環境變數
- **"查詢超時"**: 網路較慢或 API 回應慢，請增加超時時間
- **"無效的域名或 IP"**: 請檢查輸入格式是否正確

## 技術支援

如有問題或建議，請檢查：
1. 確認 Python 版本 >= 3.9
2. 確認已安裝所有必要套件
3. 確認 API 認證設定正確
4. 檢查網路連線狀況

## 更新日誌

- **v1.0**: 初始版本，支援基本的 GUI 功能
- 支援 HetrixTools 和 RBLTracker API
- 支援批次查詢和結果匯出
