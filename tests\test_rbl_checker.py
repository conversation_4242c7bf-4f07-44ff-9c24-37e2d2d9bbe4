#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RBL 檢查器測試模組
"""

import unittest
import sys
import os

# 添加專案根目錄到路徑
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.rbl_checker import RBLChecker

class TestRBLChecker(unittest.TestCase):
    """RBL 檢查器測試類別"""
    
    def setUp(self):
        """測試前設定"""
        self.rbl_checker = RBLChecker()
    
    def test_validate_domain_valid(self):
        """測試有效域名驗證"""
        valid_domains = [
            'google.com',
            'example.org',
            'test.co.uk',
            'sub.domain.com',
            'a.b.c.d.com'
        ]
        
        for domain in valid_domains:
            with self.subTest(domain=domain):
                self.assertTrue(
                    self.rbl_checker.validate_domain(domain),
                    f"域名 {domain} 應該是有效的"
                )
    
    def test_validate_domain_invalid(self):
        """測試無效域名驗證"""
        invalid_domains = [
            '',
            'invalid',
            '.com',
            'domain.',
            'domain..com',
            'domain .com',
            'domain@com',
            'very-long-domain-name-that-exceeds-the-maximum-length-limit-for-domain-names.com'
        ]
        
        for domain in invalid_domains:
            with self.subTest(domain=domain):
                self.assertFalse(
                    self.rbl_checker.validate_domain(domain),
                    f"域名 {domain} 應該是無效的"
                )
    
    def test_check_single_domain_invalid(self):
        """測試檢查無效域名"""
        result = self.rbl_checker.check_single_domain('invalid-domain')
        
        self.assertEqual(result['status'], 'error')
        self.assertEqual(result['domain'], 'invalid-domain')
        self.assertIn('無效的域名格式', result['message'])
        self.assertEqual(result['results'], {})
    
    def test_check_single_domain_structure(self):
        """測試檢查單一域名的回傳結構"""
        # 使用一個可能存在的域名進行測試
        result = self.rbl_checker.check_single_domain('example.com')
        
        # 檢查回傳結構
        self.assertIn('domain', result)
        self.assertIn('status', result)
        self.assertIn('message', result)
        self.assertIn('results', result)
        
        # 檢查域名
        self.assertEqual(result['domain'], 'example.com')
        
        # 狀態應該是 success 或 error
        self.assertIn(result['status'], ['success', 'error'])
        
        # 如果成功，應該有時間戳
        if result['status'] == 'success':
            self.assertIn('timestamp', result)
    
    def test_get_summary_stats_empty(self):
        """測試空結果的統計"""
        stats = self.rbl_checker.get_summary_stats([])
        
        expected_stats = {
            'total_domains': 0,
            'successful_checks': 0,
            'failed_checks': 0,
            'listed_domains': 0,
            'clean_domains': 0,
            'unknown_domains': 0
        }
        
        self.assertEqual(stats, expected_stats)
    
    def test_get_summary_stats_mixed_results(self):
        """測試混合結果的統計"""
        test_results = [
            {
                'domain': 'test1.com',
                'status': 'success',
                'results': {
                    'RBL1': {'status': 'LISTED'},
                    'RBL2': {'status': 'CLEAN'}
                }
            },
            {
                'domain': 'test2.com',
                'status': 'success',
                'results': {
                    'RBL1': {'status': 'CLEAN'},
                    'RBL2': {'status': 'CLEAN'}
                }
            },
            {
                'domain': 'test3.com',
                'status': 'error',
                'results': {}
            }
        ]
        
        stats = self.rbl_checker.get_summary_stats(test_results)
        
        self.assertEqual(stats['total_domains'], 3)
        self.assertEqual(stats['successful_checks'], 2)
        self.assertEqual(stats['failed_checks'], 1)
        self.assertEqual(stats['listed_domains'], 1)  # test1.com 有 LISTED
        self.assertEqual(stats['clean_domains'], 1)   # test2.com 全部 CLEAN
        self.assertEqual(stats['unknown_domains'], 0)
    
    def test_parse_mxtoolbox_response_empty(self):
        """測試解析空的 MXToolBox 回應"""
        empty_html = "<html><body></body></html>"
        result = self.rbl_checker._parse_mxtoolbox_response(empty_html, 'test.com')
        
        # 應該回傳空字典或備用結果
        self.assertIsInstance(result, dict)
    
    def test_fallback_parse(self):
        """測試備用解析方法"""
        from bs4 import BeautifulSoup
        
        empty_soup = BeautifulSoup("<html></html>", 'html.parser')
        result = self.rbl_checker._fallback_parse(empty_soup, 'test.com')
        
        # 應該為每個 RBL 服務回傳 UNKNOWN 狀態
        self.assertIsInstance(result, dict)
        
        for service in self.rbl_checker.rbl_services:
            self.assertIn(service, result)
            self.assertEqual(result[service]['status'], 'UNKNOWN')

class TestRBLCheckerIntegration(unittest.TestCase):
    """RBL 檢查器整合測試類別"""
    
    def setUp(self):
        """測試前設定"""
        self.rbl_checker = RBLChecker()
    
    @unittest.skip("需要網路連線的整合測試")
    def test_check_real_domain(self):
        """測試檢查真實域名（需要網路連線）"""
        # 這個測試需要實際的網路連線
        result = self.rbl_checker.check_single_domain('google.com')
        
        self.assertEqual(result['domain'], 'google.com')
        # Google 通常不會被列入黑名單
        self.assertEqual(result['status'], 'success')
        self.assertIsInstance(result['results'], dict)
    
    @unittest.skip("需要網路連線的整合測試")
    def test_check_multiple_domains(self):
        """測試批量檢查（需要網路連線）"""
        domains = ['google.com', 'example.com']
        results = self.rbl_checker.check_multiple_domains(domains, max_workers=2)
        
        self.assertEqual(len(results), 2)
        
        for result in results:
            self.assertIn('domain', result)
            self.assertIn('status', result)
            self.assertIn(result['domain'], domains)

if __name__ == '__main__':
    # 設定測試套件
    unittest.main(verbosity=2)
