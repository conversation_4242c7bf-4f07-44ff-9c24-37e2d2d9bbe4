@echo off
REM RBL 黑名單查詢工具 GUI 版本打包批次檔
REM 使用方式: 直接執行此批次檔

echo ========================================
echo RBL 黑名單查詢工具 GUI 版本打包工具
echo ========================================
echo.

REM 檢查 Python 是否安裝
python --version >nul 2>&1
if errorlevel 1 (
    echo 錯誤: 未找到 Python，請先安裝 Python 3.9 或更高版本
    pause
    exit /b 1
)

echo 檢查 Python 版本...
python --version

REM 檢查 PyInstaller 是否安裝
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo.
    echo 安裝 PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo 錯誤: PyInstaller 安裝失敗
        pause
        exit /b 1
    )
)

REM 檢查必要檔案
if not exist "ui.py" (
    echo 錯誤: 找不到 ui.py 檔案
    pause
    exit /b 1
)

echo.
echo 開始打包...
echo.

REM 清理舊檔案
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "*.spec" del "*.spec"

REM 執行打包
python -m PyInstaller ^
    --onefile ^
    --windowed ^
    --name=RBL_Checker_GUI ^
    --add-data="GUI_使用說明.md;." ^
    --add-data="sample_domains.txt;." ^
    --hidden-import=tkinter ^
    --hidden-import=tkinter.ttk ^
    --hidden-import=tkinter.messagebox ^
    --hidden-import=tkinter.filedialog ^
    --hidden-import=tkinter.scrolledtext ^
    --hidden-import=requests ^
    --hidden-import=concurrent.futures ^
    --hidden-import=threading ^
    --exclude-module=matplotlib ^
    --exclude-module=numpy ^
    --exclude-module=pandas ^
    --exclude-module=PIL ^
    ui.py

if errorlevel 1 (
    echo.
    echo 錯誤: 打包失敗
    pause
    exit /b 1
)

echo.
echo ========================================
echo 打包完成！
echo ========================================

if exist "dist\RBL_Checker_GUI.exe" (
    echo 檔案位置: dist\RBL_Checker_GUI.exe
    
    REM 顯示檔案大小
    for %%A in ("dist\RBL_Checker_GUI.exe") do (
        set /a size_mb=%%~zA/1024/1024
        echo 檔案大小: !size_mb! MB
    )
    
    echo.
    echo 使用方式:
    echo 1. 直接執行 dist\RBL_Checker_GUI.exe
    echo 2. 首次使用請先設定 API 認證資訊
    echo.
    
    REM 詢問是否立即執行
    set /p choice="是否立即執行程式？(y/n): "
    if /i "%choice%"=="y" (
        start "" "dist\RBL_Checker_GUI.exe"
    )
) else (
    echo 錯誤: 找不到生成的 EXE 檔案
)

echo.
pause
