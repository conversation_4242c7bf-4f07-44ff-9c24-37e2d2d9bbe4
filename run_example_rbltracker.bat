@echo off
REM run_example_rbltracker.bat - 範例批次檔，使用 RBLTracker 查詢

echo [INFO] 使用 RBLTracker 查詢 targets.txt ...
echo 請先確認已經設定環境變數 RBLTRACKER_SID 和 RBLTRACKER_TOKEN
echo -------------------------------------------------------

REM 使用 RBLTracker，輸入 targets.txt，輸出 results_rbl.json 並顯示摘要
py main.py --provider rbltracker --in targets.txt --out results_rbl.json --print

echo -------------------------------------------------------
echo 查詢完成，結果已輸出至 results_rbl.json
pause
