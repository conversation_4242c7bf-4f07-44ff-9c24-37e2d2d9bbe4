#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RBL Email Domains Status Checker 安裝設定檔
"""

from setuptools import setup, find_packages
import os

# 讀取 README 檔案
def read_readme():
    try:
        with open('README.md', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return "RBL Email Domains Status Checker"

# 讀取 requirements.txt
def read_requirements():
    try:
        with open('requirements.txt', 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    except FileNotFoundError:
        return [
            'requests>=2.31.0',
            'beautifulsoup4>=4.12.2',
            'lxml>=4.9.3',
            'pyinstaller>=6.1.0'
        ]

setup(
    name="rbl-email-domains-checker",
    version="1.0.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="一個用於檢查電子郵件域名在各種實時黑名單 (RBL) 狀態的 Python 應用程式",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/Python_RBL-email-domains",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: System Administrators",
        "Intended Audience :: Information Technology",
        "Topic :: Communications :: Email",
        "Topic :: Internet :: Name Service (DNS)",
        "Topic :: Security",
        "Topic :: System :: Networking :: Monitoring",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
        "Environment :: X11 Applications :: Qt",
        "Environment :: Win32 (MS Windows)",
        "Environment :: MacOS X",
    ],
    python_requires=">=3.7",
    install_requires=read_requirements(),
    extras_require={
        'dev': [
            'pytest>=7.0.0',
            'pytest-cov>=4.0.0',
            'flake8>=5.0.0',
            'black>=22.0.0',
            'mypy>=1.0.0',
        ],
        'dns': [
            'dnspython>=2.3.0',
        ],
        'gui': [
            'tkinter-tooltip>=2.0.0',
        ]
    },
    entry_points={
        'console_scripts': [
            'rbl-checker=main:main',
        ],
    },
    include_package_data=True,
    package_data={
        '': ['*.txt', '*.md', '*.json', '*.yml', '*.yaml'],
    },
    keywords=[
        'rbl', 'blacklist', 'email', 'domain', 'spam', 'security',
        'mxtoolbox', 'dns', 'mail', 'checker', 'gui', 'tkinter'
    ],
    project_urls={
        "Bug Reports": "https://github.com/your-username/Python_RBL-email-domains/issues",
        "Source": "https://github.com/your-username/Python_RBL-email-domains",
        "Documentation": "https://github.com/your-username/Python_RBL-email-domains/wiki",
    },
)
