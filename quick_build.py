#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速打包腳本 - 簡化版本的 EXE 打包工具
適用於 RBL 黑名單查詢工具 GUI 版本
"""

import os
import sys
import subprocess
import shutil

def main():
    """快速打包主函數"""
    print("RBL 黑名單查詢工具 GUI 版本 - 快速打包")
    print("=" * 50)
    
    # 檢查 PyInstaller
    try:
        import PyInstaller
        print(f"✓ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ 錯誤: 未安裝 PyInstaller")
        print("請執行: pip install pyinstaller")
        return 1
    
    # 檢查主程式檔案
    if not os.path.exists('ui.py'):
        print("❌ 錯誤: 找不到 ui.py 檔案")
        return 1
    
    print("✓ 檔案檢查通過")
    
    # 清理舊的建置檔案
    print("\n清理舊檔案...")
    for item in ['build', 'dist', '__pycache__']:
        if os.path.exists(item):
            shutil.rmtree(item)
            print(f"  已清理: {item}")
    
    # 清理 .spec 檔案
    for spec_file in ['RBL_Checker_GUI.spec']:
        if os.path.exists(spec_file):
            os.remove(spec_file)
            print(f"  已清理: {spec_file}")
    
    # 執行 PyInstaller 打包
    print("\n開始打包...")
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',                    # 打包成單一檔案
        '--windowed',                   # 無控制台視窗
        '--name=RBL_Checker_GUI',       # 指定檔案名稱
        '--add-data=GUI_使用說明.md;.',  # 包含使用說明
        '--add-data=sample_domains.txt;.',  # 包含範例檔案
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.ttk',
        '--hidden-import=tkinter.messagebox',
        '--hidden-import=tkinter.filedialog',
        '--hidden-import=tkinter.scrolledtext',
        '--hidden-import=requests',
        '--hidden-import=concurrent.futures',
        '--hidden-import=threading',
        '--exclude-module=matplotlib',
        '--exclude-module=numpy',
        '--exclude-module=pandas',
        '--exclude-module=PIL',
        'ui.py'
    ]
    
    try:
        print("執行命令:", ' '.join(cmd))
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ 打包成功")
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失敗: {e}")
        print(f"錯誤輸出: {e.stderr}")
        return 1
    
    # 檢查結果
    exe_path = 'dist/RBL_Checker_GUI.exe'
    if os.path.exists(exe_path):
        size_mb = os.path.getsize(exe_path) / (1024 * 1024)
        print(f"\n✓ 打包完成！")
        print(f"檔案位置: {exe_path}")
        print(f"檔案大小: {size_mb:.1f} MB")
        print(f"\n使用方式:")
        print(f"直接執行 {exe_path}")
    else:
        print("❌ 找不到生成的 EXE 檔案")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
