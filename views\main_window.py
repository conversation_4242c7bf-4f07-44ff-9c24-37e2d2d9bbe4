#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主視窗介面模組
提供應用程式的主要使用者介面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import logging
from typing import Optional

from models.rbl_checker import RBLChecker
from models.domain_validator import DomainValidator
from views.batch_window import BatchWindow
from views.result_window import ResultWindow
from utils.file_handler import FileHandler

class MainWindow:
    """主視窗類別"""
    
    def __init__(self, root: tk.Tk, config):
        self.root = root
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 初始化核心元件
        self.rbl_checker = RBLChecker()
        self.domain_validator = DomainValidator()
        self.file_handler = FileHandler()
        
        # 視窗狀態
        self.is_checking = False
        self.current_results = []
        
        # 設定主視窗
        self.setup_main_window()
        self.create_widgets()
        self.setup_layout()
        
        self.logger.info("主視窗初始化完成")
    
    def setup_main_window(self):
        """設定主視窗屬性"""
        self.root.title("RBL Email Domains Status Checker v1.0")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # 設定圖示（如果有的話）
        try:
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
        
        # 設定樣式
        style = ttk.Style()
        try:
            style.theme_use('clam')
        except:
            pass  # 如果主題不可用，使用預設主題
    
    def create_widgets(self):
        """建立所有 UI 元件"""
        # 主框架
        self.main_frame = ttk.Frame(self.root, padding="10")
        
        # 標題
        self.title_label = ttk.Label(
            self.main_frame, 
            text="RBL Email Domains Status Checker",
            font=("Arial", 16, "bold")
        )
        
        # 輸入區域
        self.input_frame = ttk.LabelFrame(self.main_frame, text="域名輸入", padding="10")
        
        self.domain_label = ttk.Label(self.input_frame, text="請輸入要檢查的域名:")
        self.domain_var = tk.StringVar()
        self.domain_entry = ttk.Entry(
            self.input_frame, 
            textvariable=self.domain_var,
            font=("Arial", 12),
            width=40
        )
        
        # 按鈕區域
        self.button_frame = ttk.Frame(self.input_frame)
        
        self.check_button = ttk.Button(
            self.button_frame,
            text="檢查 RBL 狀態",
            command=self.check_single_domain,
            style="Accent.TButton"
        )
        
        self.batch_button = ttk.Button(
            self.button_frame,
            text="批量檢查",
            command=self.open_batch_window
        )
        
        self.clear_button = ttk.Button(
            self.button_frame,
            text="清除",
            command=self.clear_input
        )
        
        # 進度條
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            self.main_frame,
            variable=self.progress_var,
            maximum=100,
            mode='determinate'
        )
        
        # 狀態標籤
        self.status_var = tk.StringVar(value="就緒")
        self.status_label = ttk.Label(
            self.main_frame,
            textvariable=self.status_var,
            font=("Arial", 10)
        )
        
        # 結果區域
        self.result_frame = ttk.LabelFrame(self.main_frame, text="檢查結果", padding="10")
        
        # 結果樹狀檢視
        self.result_tree = ttk.Treeview(
            self.result_frame,
            columns=("domain", "status", "listed_count", "timestamp"),
            show="headings",
            height=10
        )
        
        # 設定欄位標題
        self.result_tree.heading("domain", text="域名")
        self.result_tree.heading("status", text="狀態")
        self.result_tree.heading("listed_count", text="黑名單數量")
        self.result_tree.heading("timestamp", text="檢查時間")
        
        # 設定欄位寬度
        self.result_tree.column("domain", width=200)
        self.result_tree.column("status", width=100)
        self.result_tree.column("listed_count", width=100)
        self.result_tree.column("timestamp", width=150)
        
        # 滾動條
        self.tree_scrollbar = ttk.Scrollbar(
            self.result_frame,
            orient="vertical",
            command=self.result_tree.yview
        )
        self.result_tree.configure(yscrollcommand=self.tree_scrollbar.set)
        
        # 結果操作按鈕
        self.result_button_frame = ttk.Frame(self.result_frame)
        
        self.view_details_button = ttk.Button(
            self.result_button_frame,
            text="查看詳細",
            command=self.view_result_details
        )
        
        self.export_button = ttk.Button(
            self.result_button_frame,
            text="匯出結果",
            command=self.export_results
        )
        
        self.clear_results_button = ttk.Button(
            self.result_button_frame,
            text="清除結果",
            command=self.clear_results
        )
        
        # 綁定事件
        self.domain_entry.bind("<Return>", lambda e: self.check_single_domain())
        self.result_tree.bind("<Double-1>", lambda e: self.view_result_details())
    
    def setup_layout(self):
        """設定 UI 佈局"""
        # 主框架
        self.main_frame.grid(row=0, column=0, sticky="nsew")
        
        # 設定根視窗的網格權重
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)
        
        # 設定主框架的網格權重
        self.main_frame.grid_rowconfigure(3, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)
        
        # 標題
        self.title_label.grid(row=0, column=0, pady=(0, 20))
        
        # 輸入區域
        self.input_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        self.input_frame.grid_columnconfigure(0, weight=1)
        
        self.domain_label.grid(row=0, column=0, sticky="w", pady=(0, 5))
        self.domain_entry.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        
        # 按鈕區域
        self.button_frame.grid(row=2, column=0, sticky="ew")
        self.check_button.grid(row=0, column=0, padx=(0, 5))
        self.batch_button.grid(row=0, column=1, padx=5)
        self.clear_button.grid(row=0, column=2, padx=(5, 0))
        
        # 進度條和狀態
        self.progress_bar.grid(row=2, column=0, sticky="ew", pady=(10, 5))
        self.status_label.grid(row=3, column=0, sticky="w")
        
        # 結果區域
        self.result_frame.grid(row=4, column=0, sticky="nsew", pady=(10, 0))
        self.result_frame.grid_rowconfigure(0, weight=1)
        self.result_frame.grid_columnconfigure(0, weight=1)
        
        # 結果樹狀檢視
        self.result_tree.grid(row=0, column=0, sticky="nsew")
        self.tree_scrollbar.grid(row=0, column=1, sticky="ns")
        
        # 結果操作按鈕
        self.result_button_frame.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(10, 0))
        self.view_details_button.grid(row=0, column=0, padx=(0, 5))
        self.export_button.grid(row=0, column=1, padx=5)
        self.clear_results_button.grid(row=0, column=2, padx=(5, 0))
    
    def check_single_domain(self):
        """檢查單一域名"""
        domain = self.domain_var.get().strip()
        
        if not domain:
            messagebox.showwarning("警告", "請輸入要檢查的域名")
            return
        
        # 驗證域名格式
        normalized_domain = self.domain_validator.normalize_domain(domain)
        if not self.domain_validator.is_valid_domain(normalized_domain):
            messagebox.showerror("錯誤", f"無效的域名格式: {domain}")
            return
        
        # 在新執行緒中執行檢查
        self.set_checking_state(True)
        thread = threading.Thread(
            target=self._check_domain_thread,
            args=(normalized_domain,),
            daemon=True
        )
        thread.start()
    
    def _check_domain_thread(self, domain: str):
        """在背景執行緒中檢查域名"""
        try:
            self.update_status(f"正在檢查域名: {domain}")
            self.progress_var.set(50)
            
            result = self.rbl_checker.check_single_domain(domain)
            
            # 在主執行緒中更新 UI
            self.root.after(0, self._update_result_ui, result)
            
        except Exception as e:
            self.logger.error(f"檢查域名時發生錯誤: {str(e)}")
            self.root.after(0, self._show_error, f"檢查失敗: {str(e)}")
        finally:
            self.root.after(0, lambda: self.set_checking_state(False))
    
    def _update_result_ui(self, result: dict):
        """更新結果 UI"""
        self.current_results.append(result)
        self.add_result_to_tree(result)
        self.progress_var.set(100)
        self.update_status("檢查完成")
    
    def _show_error(self, message: str):
        """顯示錯誤訊息"""
        messagebox.showerror("錯誤", message)
        self.update_status("檢查失敗")
    
    def add_result_to_tree(self, result: dict):
        """將結果添加到樹狀檢視"""
        domain = result.get('domain', '')
        status = result.get('status', '')
        timestamp = result.get('timestamp', '')
        
        # 計算黑名單數量
        listed_count = 0
        if result.get('results'):
            listed_count = sum(
                1 for rbl_data in result['results'].values()
                if rbl_data.get('status') == 'LISTED'
            )
        
        # 設定狀態顯示
        status_display = "成功" if status == "success" else "失敗"
        if status == "success" and listed_count > 0:
            status_display = f"警告 ({listed_count})"
        
        # 插入到樹狀檢視
        item = self.result_tree.insert(
            "", "end",
            values=(domain, status_display, listed_count, timestamp)
        )
        
        # 設定顏色
        if status == "success":
            if listed_count > 0:
                self.result_tree.set(item, "status", f"⚠️ 警告 ({listed_count})")
            else:
                self.result_tree.set(item, "status", "✅ 正常")
        else:
            self.result_tree.set(item, "status", "❌ 失敗")
    
    def view_result_details(self):
        """查看結果詳細資訊"""
        selection = self.result_tree.selection()
        if not selection:
            messagebox.showinfo("提示", "請選擇要查看的結果")
            return
        
        # 取得選中的項目
        item = selection[0]
        domain = self.result_tree.item(item)['values'][0]
        
        # 找到對應的結果
        result = None
        for r in self.current_results:
            if r.get('domain') == domain:
                result = r
                break
        
        if result:
            # 開啟結果詳細視窗
            ResultWindow(self.root, result)
        else:
            messagebox.showerror("錯誤", "找不到對應的檢查結果")
    
    def open_batch_window(self):
        """開啟批量檢查視窗"""
        BatchWindow(self.root, self.rbl_checker, self.add_batch_results)
    
    def add_batch_results(self, results: list):
        """添加批量檢查結果"""
        for result in results:
            self.current_results.append(result)
            self.add_result_to_tree(result)
    
    def export_results(self):
        """匯出檢查結果"""
        if not self.current_results:
            messagebox.showinfo("提示", "沒有可匯出的結果")
            return
        
        # 選擇檔案
        filename = filedialog.asksaveasfilename(
            title="匯出結果",
            defaultextension=".csv",
            filetypes=[
                ("CSV 檔案", "*.csv"),
                ("文字檔案", "*.txt"),
                ("所有檔案", "*.*")
            ]
        )
        
        if filename:
            try:
                self.file_handler.export_results(self.current_results, filename)
                messagebox.showinfo("成功", f"結果已匯出到: {filename}")
            except Exception as e:
                messagebox.showerror("錯誤", f"匯出失敗: {str(e)}")
    
    def clear_input(self):
        """清除輸入"""
        self.domain_var.set("")
        self.domain_entry.focus()
    
    def clear_results(self):
        """清除所有結果"""
        if self.current_results:
            if messagebox.askyesno("確認", "確定要清除所有結果嗎？"):
                self.current_results.clear()
                self.result_tree.delete(*self.result_tree.get_children())
                self.update_status("結果已清除")
    
    def set_checking_state(self, is_checking: bool):
        """設定檢查狀態"""
        self.is_checking = is_checking
        
        # 啟用/停用按鈕
        state = "disabled" if is_checking else "normal"
        self.check_button.config(state=state)
        self.batch_button.config(state=state)
        self.domain_entry.config(state=state)
        
        if not is_checking:
            self.progress_var.set(0)
    
    def update_status(self, message: str):
        """更新狀態訊息"""
        self.status_var.set(message)
        self.root.update_idletasks()
