#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
結果詳細視窗模組
顯示單一域名的詳細 RBL 檢查結果
"""

import tkinter as tk
from tkinter import ttk, messagebox
import logging
from typing import Dict

class ResultWindow:
    """結果詳細視窗類別"""
    
    def __init__(self, parent: tk.Tk, result: Dict):
        self.parent = parent
        self.result = result
        self.logger = logging.getLogger(__name__)
        
        # 建立視窗
        self.create_window()
        self.create_widgets()
        self.setup_layout()
        self.populate_data()
        
        self.logger.info(f"結果詳細視窗初始化完成 - 域名: {result.get('domain', 'Unknown')}")
    
    def create_window(self):
        """建立結果詳細視窗"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(f"RBL 檢查結果 - {self.result.get('domain', 'Unknown')}")
        self.window.geometry("700x500")
        self.window.minsize(600, 400)
        
        # 設定為模態視窗
        self.window.transient(self.parent)
        self.window.grab_set()
        
        # 置中顯示
        self.center_window()
    
    def center_window(self):
        """將視窗置中顯示"""
        self.window.update_idletasks()
        
        # 取得視窗大小
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        
        # 取得螢幕大小
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        
        # 計算置中位置
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """建立所有 UI 元件"""
        # 主框架
        self.main_frame = ttk.Frame(self.window, padding="10")
        
        # 標題區域
        self.header_frame = ttk.Frame(self.main_frame)
        
        self.title_label = ttk.Label(
            self.header_frame,
            text=f"域名: {self.result.get('domain', 'Unknown')}",
            font=("Arial", 14, "bold")
        )
        
        # 狀態標籤
        status = self.result.get('status', 'unknown')
        status_text = "✅ 檢查成功" if status == "success" else "❌ 檢查失敗"
        status_color = "green" if status == "success" else "red"
        
        self.status_label = ttk.Label(
            self.header_frame,
            text=status_text,
            font=("Arial", 12),
            foreground=status_color
        )
        
        # 基本資訊區域
        self.info_frame = ttk.LabelFrame(self.main_frame, text="基本資訊", padding="10")
        
        # 檢查時間
        timestamp = self.result.get('timestamp', 'Unknown')
        self.time_label = ttk.Label(self.info_frame, text=f"檢查時間: {timestamp}")
        
        # 檢查訊息
        message = self.result.get('message', 'No message')
        self.message_label = ttk.Label(self.info_frame, text=f"狀態訊息: {message}")
        
        # RBL 檢查結果區域
        self.rbl_frame = ttk.LabelFrame(self.main_frame, text="RBL 檢查結果", padding="10")
        
        # 結果樹狀檢視
        self.rbl_tree = ttk.Treeview(
            self.rbl_frame,
            columns=("rbl_service", "status", "details"),
            show="headings",
            height=12
        )
        
        # 設定欄位標題
        self.rbl_tree.heading("rbl_service", text="RBL 服務")
        self.rbl_tree.heading("status", text="狀態")
        self.rbl_tree.heading("details", text="詳細資訊")
        
        # 設定欄位寬度
        self.rbl_tree.column("rbl_service", width=200)
        self.rbl_tree.column("status", width=100)
        self.rbl_tree.column("details", width=300)
        
        # 滾動條
        self.rbl_scrollbar = ttk.Scrollbar(
            self.rbl_frame,
            orient="vertical",
            command=self.rbl_tree.yview
        )
        self.rbl_tree.configure(yscrollcommand=self.rbl_scrollbar.set)
        
        # 統計資訊區域
        self.stats_frame = ttk.LabelFrame(self.main_frame, text="統計資訊", padding="10")
        
        # 統計標籤
        self.total_label = ttk.Label(self.stats_frame, text="總檢查數: 0")
        self.listed_label = ttk.Label(self.stats_frame, text="黑名單數: 0", foreground="red")
        self.clean_label = ttk.Label(self.stats_frame, text="正常數: 0", foreground="green")
        self.unknown_label = ttk.Label(self.stats_frame, text="未知數: 0", foreground="orange")
        
        # 按鈕區域
        self.button_frame = ttk.Frame(self.main_frame)
        
        self.copy_button = ttk.Button(
            self.button_frame,
            text="複製結果",
            command=self.copy_results
        )
        
        self.export_button = ttk.Button(
            self.button_frame,
            text="匯出詳細",
            command=self.export_details
        )
        
        self.close_button = ttk.Button(
            self.button_frame,
            text="關閉",
            command=self.window.destroy
        )
    
    def setup_layout(self):
        """設定 UI 佈局"""
        # 主框架
        self.main_frame.grid(row=0, column=0, sticky="nsew")
        
        # 設定視窗的網格權重
        self.window.grid_rowconfigure(0, weight=1)
        self.window.grid_columnconfigure(0, weight=1)
        
        # 設定主框架的網格權重
        self.main_frame.grid_rowconfigure(2, weight=1)
        self.main_frame.grid_columnconfigure(0, weight=1)
        
        # 標題區域
        self.header_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        self.title_label.grid(row=0, column=0, sticky="w")
        self.status_label.grid(row=1, column=0, sticky="w", pady=(5, 0))
        
        # 基本資訊區域
        self.info_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        self.info_frame.grid_columnconfigure(0, weight=1)
        
        self.time_label.grid(row=0, column=0, sticky="w", pady=(0, 5))
        self.message_label.grid(row=1, column=0, sticky="w")
        
        # RBL 檢查結果區域
        self.rbl_frame.grid(row=2, column=0, sticky="nsew", pady=(0, 10))
        self.rbl_frame.grid_rowconfigure(0, weight=1)
        self.rbl_frame.grid_columnconfigure(0, weight=1)
        
        # 結果樹狀檢視
        self.rbl_tree.grid(row=0, column=0, sticky="nsew")
        self.rbl_scrollbar.grid(row=0, column=1, sticky="ns")
        
        # 統計資訊區域
        self.stats_frame.grid(row=3, column=0, sticky="ew", pady=(0, 10))
        
        # 統計標籤佈局
        self.total_label.grid(row=0, column=0, padx=(0, 20), sticky="w")
        self.listed_label.grid(row=0, column=1, padx=(0, 20), sticky="w")
        self.clean_label.grid(row=0, column=2, padx=(0, 20), sticky="w")
        self.unknown_label.grid(row=0, column=3, sticky="w")
        
        # 按鈕區域
        self.button_frame.grid(row=4, column=0, sticky="ew")
        self.copy_button.grid(row=0, column=0, padx=(0, 5))
        self.export_button.grid(row=0, column=1, padx=5)
        self.close_button.grid(row=0, column=2, padx=(5, 0))
    
    def populate_data(self):
        """填充資料到 UI 元件"""
        rbl_results = self.result.get('results', {})
        
        # 統計資訊
        total_count = len(rbl_results)
        listed_count = 0
        clean_count = 0
        unknown_count = 0
        
        # 填充 RBL 結果
        for rbl_service, rbl_data in rbl_results.items():
            status = rbl_data.get('status', 'UNKNOWN')
            details = rbl_data.get('details', 'No details available')
            
            # 設定狀態顯示
            if status == 'LISTED':
                status_display = "❌ 已列入"
                listed_count += 1
            elif status == 'CLEAN':
                status_display = "✅ 正常"
                clean_count += 1
            else:
                status_display = "❓ 未知"
                unknown_count += 1
            
            # 插入到樹狀檢視
            item = self.rbl_tree.insert(
                "", "end",
                values=(rbl_service, status_display, details)
            )
            
            # 設定行顏色
            if status == 'LISTED':
                self.rbl_tree.set(item, "status", "❌ 已列入")
            elif status == 'CLEAN':
                self.rbl_tree.set(item, "status", "✅ 正常")
        
        # 更新統計標籤
        self.total_label.config(text=f"總檢查數: {total_count}")
        self.listed_label.config(text=f"黑名單數: {listed_count}")
        self.clean_label.config(text=f"正常數: {clean_count}")
        self.unknown_label.config(text=f"未知數: {unknown_count}")
        
        # 如果沒有結果，顯示提示
        if not rbl_results:
            self.rbl_tree.insert(
                "", "end",
                values=("無資料", "檢查失敗或無結果", "請檢查網路連線或稍後重試")
            )
    
    def copy_results(self):
        """複製結果到剪貼簿"""
        try:
            # 建立結果文字
            result_text = self.format_results_text()
            
            # 複製到剪貼簿
            self.window.clipboard_clear()
            self.window.clipboard_append(result_text)
            
            messagebox.showinfo("成功", "結果已複製到剪貼簿")
            
        except Exception as e:
            messagebox.showerror("錯誤", f"複製失敗: {str(e)}")
    
    def export_details(self):
        """匯出詳細結果"""
        from tkinter import filedialog
        
        filename = filedialog.asksaveasfilename(
            title="匯出詳細結果",
            defaultextension=".txt",
            filetypes=[
                ("文字檔案", "*.txt"),
                ("CSV 檔案", "*.csv"),
                ("所有檔案", "*.*")
            ]
        )
        
        if filename:
            try:
                result_text = self.format_results_text()
                
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(result_text)
                
                messagebox.showinfo("成功", f"詳細結果已匯出到: {filename}")
                
            except Exception as e:
                messagebox.showerror("錯誤", f"匯出失敗: {str(e)}")
    
    def format_results_text(self) -> str:
        """格式化結果文字"""
        domain = self.result.get('domain', 'Unknown')
        status = self.result.get('status', 'unknown')
        timestamp = self.result.get('timestamp', 'Unknown')
        message = self.result.get('message', 'No message')
        
        lines = [
            f"RBL 檢查結果詳細報告",
            f"=" * 50,
            f"域名: {domain}",
            f"檢查狀態: {status}",
            f"檢查時間: {timestamp}",
            f"狀態訊息: {message}",
            f"",
            f"RBL 服務檢查結果:",
            f"-" * 50
        ]
        
        rbl_results = self.result.get('results', {})
        if rbl_results:
            for rbl_service, rbl_data in rbl_results.items():
                status = rbl_data.get('status', 'UNKNOWN')
                details = rbl_data.get('details', 'No details available')
                
                lines.append(f"服務: {rbl_service}")
                lines.append(f"  狀態: {status}")
                lines.append(f"  詳細: {details}")
                lines.append("")
        else:
            lines.append("無 RBL 檢查結果")
        
        return "\n".join(lines)
