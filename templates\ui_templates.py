#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI 模板和樣式模組
提供統一的 UI 樣式和模板
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any

class UITemplates:
    """UI 模板類別"""
    
    # 顏色配置
    COLORS = {
        'primary': '#2196F3',
        'secondary': '#FFC107',
        'success': '#4CAF50',
        'warning': '#FF9800',
        'error': '#F44336',
        'info': '#00BCD4',
        'light': '#F5F5F5',
        'dark': '#212121',
        'background': '#FFFFFF',
        'text': '#212121',
        'text_secondary': '#757575'
    }
    
    # 字型配置
    FONTS = {
        'default': ('Arial', 10),
        'heading': ('Arial', 14, 'bold'),
        'title': ('Arial', 16, 'bold'),
        'small': ('Arial', 8),
        'monospace': ('Consolas', 10)
    }
    
    # 間距配置
    SPACING = {
        'small': 5,
        'medium': 10,
        'large': 20,
        'xlarge': 30
    }
    
    @classmethod
    def setup_styles(cls, root: tk.Tk) -> ttk.Style:
        """設定 TTK 樣式"""
        style = ttk.Style()
        
        # 嘗試使用現代主題
        try:
            available_themes = style.theme_names()
            if 'clam' in available_themes:
                style.theme_use('clam')
            elif 'alt' in available_themes:
                style.theme_use('alt')
        except:
            pass
        
        # 自訂樣式
        cls._configure_button_styles(style)
        cls._configure_frame_styles(style)
        cls._configure_label_styles(style)
        cls._configure_entry_styles(style)
        cls._configure_treeview_styles(style)
        cls._configure_progressbar_styles(style)
        
        return style
    
    @classmethod
    def _configure_button_styles(cls, style: ttk.Style):
        """設定按鈕樣式"""
        # 主要按鈕
        style.configure(
            'Accent.TButton',
            background=cls.COLORS['primary'],
            foreground='white',
            font=cls.FONTS['default'],
            padding=(10, 5)
        )
        
        # 成功按鈕
        style.configure(
            'Success.TButton',
            background=cls.COLORS['success'],
            foreground='white',
            font=cls.FONTS['default'],
            padding=(10, 5)
        )
        
        # 警告按鈕
        style.configure(
            'Warning.TButton',
            background=cls.COLORS['warning'],
            foreground='white',
            font=cls.FONTS['default'],
            padding=(10, 5)
        )
        
        # 錯誤按鈕
        style.configure(
            'Error.TButton',
            background=cls.COLORS['error'],
            foreground='white',
            font=cls.FONTS['default'],
            padding=(10, 5)
        )
    
    @classmethod
    def _configure_frame_styles(cls, style: ttk.Style):
        """設定框架樣式"""
        style.configure(
            'Card.TFrame',
            background=cls.COLORS['background'],
            relief='solid',
            borderwidth=1
        )
        
        style.configure(
            'Sidebar.TFrame',
            background=cls.COLORS['light']
        )
    
    @classmethod
    def _configure_label_styles(cls, style: ttk.Style):
        """設定標籤樣式"""
        # 標題標籤
        style.configure(
            'Title.TLabel',
            font=cls.FONTS['title'],
            foreground=cls.COLORS['text']
        )
        
        # 標題標籤
        style.configure(
            'Heading.TLabel',
            font=cls.FONTS['heading'],
            foreground=cls.COLORS['text']
        )
        
        # 成功標籤
        style.configure(
            'Success.TLabel',
            foreground=cls.COLORS['success'],
            font=cls.FONTS['default']
        )
        
        # 警告標籤
        style.configure(
            'Warning.TLabel',
            foreground=cls.COLORS['warning'],
            font=cls.FONTS['default']
        )
        
        # 錯誤標籤
        style.configure(
            'Error.TLabel',
            foreground=cls.COLORS['error'],
            font=cls.FONTS['default']
        )
        
        # 次要文字標籤
        style.configure(
            'Secondary.TLabel',
            foreground=cls.COLORS['text_secondary'],
            font=cls.FONTS['small']
        )
    
    @classmethod
    def _configure_entry_styles(cls, style: ttk.Style):
        """設定輸入框樣式"""
        style.configure(
            'Search.TEntry',
            fieldbackground='white',
            borderwidth=2,
            relief='solid'
        )
    
    @classmethod
    def _configure_treeview_styles(cls, style: ttk.Style):
        """設定樹狀檢視樣式"""
        style.configure(
            'Results.Treeview',
            background='white',
            foreground=cls.COLORS['text'],
            fieldbackground='white',
            font=cls.FONTS['default']
        )
        
        style.configure(
            'Results.Treeview.Heading',
            font=cls.FONTS['default'],
            background=cls.COLORS['light']
        )
    
    @classmethod
    def _configure_progressbar_styles(cls, style: ttk.Style):
        """設定進度條樣式"""
        style.configure(
            'Progress.TProgressbar',
            background=cls.COLORS['primary'],
            troughcolor=cls.COLORS['light'],
            borderwidth=0,
            lightcolor=cls.COLORS['primary'],
            darkcolor=cls.COLORS['primary']
        )
    
    @classmethod
    def create_status_frame(cls, parent: tk.Widget, text: str = "狀態") -> ttk.LabelFrame:
        """建立狀態框架"""
        frame = ttk.LabelFrame(parent, text=text, padding=cls.SPACING['medium'])
        return frame
    
    @classmethod
    def create_action_frame(cls, parent: tk.Widget) -> ttk.Frame:
        """建立動作按鈕框架"""
        frame = ttk.Frame(parent)
        return frame
    
    @classmethod
    def create_info_label(cls, parent: tk.Widget, text: str, 
                         style_type: str = 'info') -> ttk.Label:
        """建立資訊標籤"""
        style_map = {
            'info': 'TLabel',
            'success': 'Success.TLabel',
            'warning': 'Warning.TLabel',
            'error': 'Error.TLabel',
            'title': 'Title.TLabel',
            'heading': 'Heading.TLabel',
            'secondary': 'Secondary.TLabel'
        }
        
        style = style_map.get(style_type, 'TLabel')
        label = ttk.Label(parent, text=text, style=style)
        return label
    
    @classmethod
    def create_action_button(cls, parent: tk.Widget, text: str, command,
                           style_type: str = 'default') -> ttk.Button:
        """建立動作按鈕"""
        style_map = {
            'default': 'TButton',
            'primary': 'Accent.TButton',
            'success': 'Success.TButton',
            'warning': 'Warning.TButton',
            'error': 'Error.TButton'
        }
        
        style = style_map.get(style_type, 'TButton')
        button = ttk.Button(parent, text=text, command=command, style=style)
        return button
    
    @classmethod
    def create_separator(cls, parent: tk.Widget, orient: str = 'horizontal') -> ttk.Separator:
        """建立分隔線"""
        separator = ttk.Separator(parent, orient=orient)
        return separator
    
    @classmethod
    def apply_grid_weights(cls, widget: tk.Widget, rows: list = None, 
                          columns: list = None):
        """應用網格權重"""
        if rows:
            for row in rows:
                widget.grid_rowconfigure(row, weight=1)
        
        if columns:
            for column in columns:
                widget.grid_columnconfigure(column, weight=1)
    
    @classmethod
    def center_window(cls, window: tk.Toplevel, width: int = None, 
                     height: int = None):
        """將視窗置中"""
        window.update_idletasks()
        
        if width is None:
            width = window.winfo_width()
        if height is None:
            height = window.winfo_height()
        
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        window.geometry(f"{width}x{height}+{x}+{y}")
    
    @classmethod
    def get_icon_text(cls, icon_type: str) -> str:
        """取得圖示文字"""
        icons = {
            'success': '✅',
            'error': '❌',
            'warning': '⚠️',
            'info': 'ℹ️',
            'question': '❓',
            'check': '✓',
            'cross': '✗',
            'loading': '⏳',
            'search': '🔍',
            'export': '📤',
            'import': '📥',
            'settings': '⚙️',
            'help': '❓'
        }
        
        return icons.get(icon_type, '')

class MessageTemplates:
    """訊息模板類別"""
    
    @staticmethod
    def format_error_message(error: str, context: str = "") -> str:
        """格式化錯誤訊息"""
        if context:
            return f"錯誤: {error}\n\n內容: {context}"
        return f"錯誤: {error}"
    
    @staticmethod
    def format_success_message(message: str, details: str = "") -> str:
        """格式化成功訊息"""
        if details:
            return f"成功: {message}\n\n詳細: {details}"
        return f"成功: {message}"
    
    @staticmethod
    def format_warning_message(warning: str, suggestion: str = "") -> str:
        """格式化警告訊息"""
        if suggestion:
            return f"警告: {warning}\n\n建議: {suggestion}"
        return f"警告: {warning}"
    
    @staticmethod
    def format_confirmation_message(action: str, consequences: str = "") -> str:
        """格式化確認訊息"""
        message = f"確定要{action}嗎？"
        if consequences:
            message += f"\n\n注意: {consequences}"
        return message
    
    @staticmethod
    def format_progress_message(current: int, total: int, item: str = "項目") -> str:
        """格式化進度訊息"""
        percentage = (current / total * 100) if total > 0 else 0
        return f"處理中... {current}/{total} {item} ({percentage:.1f}%)"
    
    @staticmethod
    def format_validation_message(errors: list) -> str:
        """格式化驗證錯誤訊息"""
        if not errors:
            return "驗證通過"
        
        message = "發現以下問題:\n\n"
        for i, error in enumerate(errors, 1):
            message += f"{i}. {error}\n"
        
        return message.strip()
