# RBL Email Domains Status Checker 開發文件

## 專案概述

RBL Email Domains Status Checker 是一個用於檢查電子郵件域名在各種實時黑名單 (RBL - Real-time Blackhole List) 狀態的 Python 應用程式。

## 技術架構

### 核心技術棧
- **Python 3.7+**: 主要程式語言
- **Tkinter**: GUI 框架
- **Requests**: HTTP 請求處理
- **BeautifulSoup4**: HTML 解析
- **PyInstaller**: 可執行檔案打包

### 架構設計

```
RBL Checker
├── 展示層 (Views)
│   ├── 主視窗 (MainWindow)
│   ├── 批量檢查視窗 (BatchWindow)
│   └── 結果詳細視窗 (ResultWindow)
├── 業務邏輯層 (Models)
│   ├── RBL 檢查器 (RBLChecker)
│   └── 域名驗證器 (DomainValidator)
├── 工具層 (Utils)
│   ├── 設定管理 (Config)
│   └── 檔案處理 (FileHandler)
└── 模板層 (Templates)
    └── UI 模板 (UITemplates)
```

## 專案結構詳解

### 目錄結構
```
Python_RBL-email-domains/
├── main.py                 # 主程式入口
├── models/                 # 業務邏輯模組
│   ├── __init__.py
│   ├── rbl_checker.py      # RBL 檢查核心邏輯
│   └── domain_validator.py # 域名驗證模組
├── views/                  # 使用者介面模組
│   ├── __init__.py
│   ├── main_window.py      # 主視窗介面
│   ├── batch_window.py     # 批量檢查視窗
│   └── result_window.py    # 結果顯示視窗
├── templates/              # UI 模板和樣式
│   ├── __init__.py
│   └── ui_templates.py     # UI 模板和樣式
├── utils/                  # 工具模組
│   ├── __init__.py
│   ├── file_handler.py     # 檔案處理工具
│   └── config.py           # 設定檔管理
├── tests/                  # 測試模組
│   ├── __init__.py
│   ├── test_rbl_checker.py
│   └── test_domain_validator.py
├── requirements.txt        # 相依套件清單
├── setup.py               # 安裝設定檔
├── build_exe.py           # 打包腳本
├── run_tests.py           # 測試執行腳本
├── start.bat              # Windows 啟動腳本
├── start.sh               # Linux/macOS 啟動腳本
├── sample_domains.txt     # 範例域名檔案
├── README.md              # 專案說明文件
├── DEVELOPMENT.md         # 開發文件
└── .gitignore             # Git 忽略檔案清單
```

### 核心模組說明

#### 1. RBLChecker (models/rbl_checker.py)
負責執行實際的 RBL 查詢和結果處理。

**主要功能:**
- 驗證域名格式
- 查詢 MXToolBox.com 的黑名單狀態
- 解析 HTML 回應
- 批量處理多個域名
- 產生統計報告

**關鍵方法:**
- `check_single_domain(domain)`: 檢查單一域名
- `check_multiple_domains(domains, max_workers)`: 批量檢查
- `get_summary_stats(results)`: 產生統計資訊

#### 2. DomainValidator (models/domain_validator.py)
提供域名格式驗證和相關工具函數。

**主要功能:**
- 域名格式驗證
- 電子郵件格式驗證
- 從 URL 或電子郵件提取域名
- 域名標準化
- 提供修正建議

#### 3. MainWindow (views/main_window.py)
應用程式的主要使用者介面。

**主要功能:**
- 單一域名檢查介面
- 結果顯示和管理
- 批量檢查入口
- 結果匯出功能

#### 4. FileHandler (utils/file_handler.py)
處理檔案讀寫和結果匯出。

**支援格式:**
- CSV: 逗號分隔值
- JSON: JavaScript 物件表示法
- XML: 可擴展標記語言
- TXT: 純文字格式

## 開發環境設定

### 1. 環境需求
- Python 3.7 或更新版本
- pip 套件管理器
- Git 版本控制

### 2. 安裝步驟
```bash
# 克隆專案
git clone https://github.com/your-username/Python_RBL-email-domains.git
cd Python_RBL-email-domains

# 建立虛擬環境
python -m venv venv

# 啟動虛擬環境
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate

# 安裝相依套件
pip install -r requirements.txt

# 安裝開發相依套件
pip install -e .[dev]
```

### 3. 執行應用程式
```bash
python main.py
```

## 測試

### 執行所有測試
```bash
python run_tests.py
```

### 執行特定測試模組
```bash
python run_tests.py test_rbl_checker
python run_tests.py test_domain_validator
```

### 使用 unittest 直接執行
```bash
python -m unittest tests.test_rbl_checker
python -m unittest tests.test_domain_validator
```

## 打包部署

### 打包成 exe 檔案
```bash
python build_exe.py
```

打包完成後會產生：
- `dist/RBL_Checker.exe`: 單一可執行檔案
- `RBL_Checker_Portable/`: 可攜式套件目錄

### 手動使用 PyInstaller
```bash
# 安裝 PyInstaller
pip install pyinstaller

# 打包成單一檔案
pyinstaller --onefile --windowed --name "RBL_Checker" main.py

# 使用自訂規格檔案
pyinstaller RBL_Checker.spec
```

## 程式碼風格

### 編碼規範
- 遵循 PEP 8 Python 編碼風格指南
- 使用 UTF-8 編碼
- 函數和變數使用 snake_case 命名
- 類別使用 PascalCase 命名
- 常數使用 UPPER_CASE 命名

### 文件字串
- 所有模組、類別和函數都應該有適當的文件字串
- 使用中文註解說明複雜邏輯
- 包含參數說明和回傳值說明

### 錯誤處理
- 使用適當的例外處理
- 記錄錯誤日誌
- 提供使用者友善的錯誤訊息

## 新功能開發

### 新增 RBL 服務
1. 在 `RBLChecker` 類別中新增服務端點
2. 更新 `rbl_services` 列表
3. 實作對應的解析邏輯
4. 新增相關測試

### 新增匯出格式
1. 在 `FileHandler` 類別中新增匯出方法
2. 更新 `export_results` 方法
3. 新增格式偵測邏輯
4. 新增相關測試

### 新增 UI 元件
1. 在 `views/` 目錄下建立新的視窗模組
2. 使用 `UITemplates` 提供的樣式
3. 遵循現有的 UI 設計模式
4. 新增事件處理邏輯

## 疑難排解

### 常見問題

**Q: 打包後的 exe 檔案很大**
A: 這是正常現象，因為包含了 Python 執行環境和所有相依套件。可以使用 UPX 壓縮來減小檔案大小。

**Q: 在某些電腦上無法執行 exe 檔案**
A: 可能是缺少 Visual C++ Redistributable。建議在目標電腦上安裝最新版本。

**Q: 網路請求失敗**
A: 檢查防火牆設定，確保允許程式存取網路。某些企業網路可能會阻擋對外連線。

**Q: GUI 在高 DPI 螢幕上顯示異常**
A: 這是 Tkinter 的已知問題。可以在程式啟動時設定 DPI 感知。

### 除錯技巧

1. **啟用詳細日誌記錄**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **使用 Python 偵錯器**
   ```python
   import pdb; pdb.set_trace()
   ```

3. **檢查網路請求**
   ```python
   import requests
   response = requests.get(url)
   print(response.status_code, response.text)
   ```

## 貢獻指南

### 提交程式碼
1. Fork 專案到您的 GitHub 帳號
2. 建立功能分支: `git checkout -b feature/amazing-feature`
3. 提交變更: `git commit -m 'Add amazing feature'`
4. 推送到分支: `git push origin feature/amazing-feature`
5. 開啟 Pull Request

### 程式碼審查
- 確保所有測試通過
- 遵循程式碼風格指南
- 新增適當的測試覆蓋
- 更新相關文件

### 問題回報
- 使用 GitHub Issues 回報問題
- 提供詳細的重現步驟
- 包含錯誤訊息和日誌
- 說明預期行為和實際行為

## 版本發布

### 版本號規則
使用語意化版本控制 (Semantic Versioning):
- MAJOR.MINOR.PATCH
- 例如: 1.0.0, 1.1.0, 1.1.1

### 發布檢查清單
- [ ] 所有測試通過
- [ ] 更新版本號
- [ ] 更新 CHANGELOG
- [ ] 建立 Git 標籤
- [ ] 打包可執行檔案
- [ ] 更新文件
- [ ] 發布 GitHub Release

## 授權資訊

此專案採用 MIT 授權條款。詳見 LICENSE 檔案。
