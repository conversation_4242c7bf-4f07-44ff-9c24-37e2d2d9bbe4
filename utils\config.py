#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
設定檔模組
管理應用程式的設定參數
"""

import os
import json
import logging
from typing import Dict, Any, Optional

class Config:
    """設定管理類別"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.logger = logging.getLogger(__name__)
        
        # 預設設定
        self.default_config = {
            "app": {
                "name": "RBL Email Domains Status Checker",
                "version": "1.0.0",
                "author": "Your Name",
                "window_width": 800,
                "window_height": 600,
                "theme": "clam"
            },
            "rbl": {
                "timeout": 30,
                "max_workers": 5,
                "retry_count": 3,
                "retry_delay": 1.0,
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            },
            "mxtoolbox": {
                "base_url": "https://mxtoolbox.com",
                "blacklist_url": "https://mxtoolbox.com/blacklists.aspx",
                "rate_limit": 1.0  # 每秒最大請求數
            },
            "ui": {
                "font_family": "Arial",
                "font_size": 10,
                "progress_update_interval": 100,
                "auto_save_results": True
            },
            "export": {
                "default_format": "csv",
                "include_timestamp": True,
                "include_details": True,
                "csv_delimiter": ",",
                "csv_encoding": "utf-8"
            },
            "logging": {
                "level": "INFO",
                "file": "rbl_checker.log",
                "max_size": 10485760,  # 10MB
                "backup_count": 5,
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            }
        }
        
        # 載入設定
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """載入設定檔"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                
                # 合併使用者設定和預設設定
                config = self.merge_config(self.default_config, user_config)
                self.logger.info(f"已載入設定檔: {self.config_file}")
                return config
            else:
                self.logger.info("設定檔不存在，使用預設設定")
                return self.default_config.copy()
                
        except Exception as e:
            self.logger.error(f"載入設定檔失敗: {str(e)}")
            return self.default_config.copy()
    
    def save_config(self) -> bool:
        """儲存設定檔"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            
            self.logger.info(f"設定已儲存到: {self.config_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"儲存設定檔失敗: {str(e)}")
            return False
    
    def merge_config(self, default: Dict, user: Dict) -> Dict:
        """合併設定字典"""
        result = default.copy()
        
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self.merge_config(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """取得設定值（支援點號路徑）"""
        try:
            keys = key_path.split('.')
            value = self.config
            
            for key in keys:
                value = value[key]
            
            return value
            
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any) -> bool:
        """設定值（支援點號路徑）"""
        try:
            keys = key_path.split('.')
            config = self.config
            
            # 導航到最後一個鍵的父級
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            
            # 設定值
            config[keys[-1]] = value
            return True
            
        except Exception as e:
            self.logger.error(f"設定值失敗: {str(e)}")
            return False
    
    def get_app_config(self) -> Dict[str, Any]:
        """取得應用程式設定"""
        return self.get('app', {})
    
    def get_rbl_config(self) -> Dict[str, Any]:
        """取得 RBL 設定"""
        return self.get('rbl', {})
    
    def get_mxtoolbox_config(self) -> Dict[str, Any]:
        """取得 MXToolBox 設定"""
        return self.get('mxtoolbox', {})
    
    def get_ui_config(self) -> Dict[str, Any]:
        """取得 UI 設定"""
        return self.get('ui', {})
    
    def get_export_config(self) -> Dict[str, Any]:
        """取得匯出設定"""
        return self.get('export', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """取得日誌設定"""
        return self.get('logging', {})
    
    def reset_to_default(self) -> bool:
        """重設為預設設定"""
        try:
            self.config = self.default_config.copy()
            self.logger.info("設定已重設為預設值")
            return True
            
        except Exception as e:
            self.logger.error(f"重設設定失敗: {str(e)}")
            return False
    
    def validate_config(self) -> bool:
        """驗證設定的有效性"""
        try:
            # 檢查必要的設定項目
            required_keys = [
                'app.name',
                'app.version',
                'rbl.timeout',
                'mxtoolbox.base_url'
            ]
            
            for key in required_keys:
                if self.get(key) is None:
                    self.logger.error(f"缺少必要的設定項目: {key}")
                    return False
            
            # 檢查數值範圍
            timeout = self.get('rbl.timeout', 0)
            if timeout <= 0 or timeout > 300:
                self.logger.error(f"無效的超時設定: {timeout}")
                return False
            
            max_workers = self.get('rbl.max_workers', 0)
            if max_workers <= 0 or max_workers > 20:
                self.logger.error(f"無效的工作執行緒數設定: {max_workers}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"驗證設定時發生錯誤: {str(e)}")
            return False
    
    def create_sample_config(self) -> bool:
        """建立範例設定檔"""
        try:
            sample_file = "config.sample.json"
            with open(sample_file, 'w', encoding='utf-8') as f:
                json.dump(self.default_config, f, indent=4, ensure_ascii=False)
            
            self.logger.info(f"範例設定檔已建立: {sample_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"建立範例設定檔失敗: {str(e)}")
            return False
